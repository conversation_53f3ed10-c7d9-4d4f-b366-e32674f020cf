import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// A utility class for validating task scheduling dates based on task constraints.
///
/// Implements the scheduling logic with the following constraints:
/// - Date must not be in the past (must be today or future)
/// - If task has range constraints, date must be within range (rangeStart to rangeEnd)
/// - If task has expiry, date must be before or equal to expiry date
/// - All applicable constraints must be satisfied for valid scheduling
class TaskScheduleValidator {
  // Private constructor to prevent instantiation
  TaskScheduleValidator._();

  /// Validates if a selected date is valid for scheduling a specific task.
  ///
  /// Validates that:
  /// - Date must not be in the past (must be today or future)
  /// - If task has range constraints, date must be within range (rangeStart to rangeEnd)
  /// - If task has expiry, date must be before or equal to expiry date
  /// - All constraints must be satisfied if present
  ///
  /// [selectedDate]: The date to validate for scheduling
  /// [task]: The task to validate against
  ///
  /// Returns `true` if the date is valid for scheduling, `false` otherwise.
  static bool isDateValidForScheduling(DateTime selectedDate, TaskDetail task) {
    final rangeStart = task.rangeStart;
    final rangeEnd = task.rangeEnd;
    final expires = task.expires;

    // Strip time components for date-only comparison
    final selectedDateOnly =
        DateTime(selectedDate.year, selectedDate.month, selectedDate.day);
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);

    // Check if date is not in the past
    bool notPastDate = selectedDateOnly.isAfter(todayOnly) ||
        selectedDateOnly.isAtSameMomentAs(todayOnly);

    // Check if date is within range (rangeStart to rangeEnd)
    bool withinRange = true; // Default to true if no range constraints
    if (rangeStart != null && rangeEnd != null) {
      final rangeStartOnly =
          DateTime(rangeStart.year, rangeStart.month, rangeStart.day);
      final rangeEndOnly =
          DateTime(rangeEnd.year, rangeEnd.month, rangeEnd.day);

      withinRange = (selectedDateOnly.isAfter(rangeStartOnly) ||
              selectedDateOnly.isAtSameMomentAs(rangeStartOnly)) &&
          (selectedDateOnly.isBefore(rangeEndOnly) ||
              selectedDateOnly.isAtSameMomentAs(rangeEndOnly));
    }

    // Check if date is before or equal to expiry
    bool beforeExpiry = true; // Default to true if no expiry constraint
    if (expires != null) {
      final expiresOnly = DateTime(expires.year, expires.month, expires.day);
      beforeExpiry = selectedDateOnly.isBefore(expiresOnly) ||
          selectedDateOnly.isAtSameMomentAs(expiresOnly);
    }

    // All constraints must be satisfied
    return withinRange && beforeExpiry;
  }

  /// Gets a list of tasks that are invalid for the selected date.
  ///
  /// [selectedDate]: The date to validate against
  /// [tasks]: List of tasks to validate
  ///
  /// Returns a list of tasks that cannot be scheduled on the selected date.
  static List<TaskDetail> getInvalidTasksForDate(
      DateTime selectedDate, List<TaskDetail> tasks) {
    return tasks
        .where((task) => !isDateValidForScheduling(selectedDate, task))
        .toList();
  }

  /// Gets a list of tasks that are valid for the selected date.
  ///
  /// [selectedDate]: The date to validate against
  /// [tasks]: List of tasks to validate
  ///
  /// Returns a list of tasks that can be scheduled on the selected date.
  static List<TaskDetail> getValidTasksForDate(
      DateTime selectedDate, List<TaskDetail> tasks) {
    return tasks
        .where((task) => isDateValidForScheduling(selectedDate, task))
        .toList();
  }

  /// Creates a human-readable description of why a task cannot be scheduled on a date.
  ///
  /// [selectedDate]: The date that was selected
  /// [task]: The task that failed validation
  ///
  /// Returns a descriptive string explaining the validation failure.
  static String getValidationFailureReason(
      DateTime selectedDate, TaskDetail task) {
    final rangeStart = task.rangeStart;
    final rangeEnd = task.rangeEnd;
    final expires = task.expires;
    final storeName = task.storeName ?? 'Unknown Store';

    final selectedDateOnly =
        DateTime(selectedDate.year, selectedDate.month, selectedDate.day);
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);

    List<String> reasons = [];

    // Check past date constraint
    if (selectedDateOnly.isBefore(todayOnly)) {
      reasons.add('Cannot schedule to past dates');
    }

    // Check range constraint
    if (rangeStart != null && rangeEnd != null) {
      final rangeStartOnly =
          DateTime(rangeStart.year, rangeStart.month, rangeStart.day);
      final rangeEndOnly =
          DateTime(rangeEnd.year, rangeEnd.month, rangeEnd.day);

      if (selectedDateOnly.isBefore(rangeStartOnly)) {
        reasons.add(
            'Selected date is before task range start (${_formatDate(rangeStart)})');
      } else if (selectedDateOnly.isAfter(rangeEndOnly)) {
        reasons.add(
            'Selected date is after task range end (${_formatDate(rangeEnd)})');
      }
    }

    // Check expiry constraint
    if (expires != null) {
      final expiresOnly = DateTime(expires.year, expires.month, expires.day);
      if (selectedDateOnly.isAfter(expiresOnly)) {
        reasons.add(
            'Selected date is after task expiry (${_formatDate(expires)})');
      }
    }

    if (reasons.isEmpty) {
      reasons.add('Date validation failed');
    }

    return '$storeName: ${reasons.join(', ')}';
  }

  /// Formats a DateTime for user-friendly display.
  static String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }
}
