// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'downloaded_file_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class DownloadedFileModel extends _DownloadedFileModel
    with RealmEntity, RealmObjectBase, RealmObject {
  DownloadedFileModel(
    String id,
    String fileType,
    String originalUrl,
    String localPath,
    DateTime downloadDate,
    bool isDownloaded, {
    int? fileSize,
    DateTime? lastAccessDate,
  }) {
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'fileType', fileType);
    RealmObjectBase.set(this, 'originalUrl', originalUrl);
    RealmObjectBase.set(this, 'localPath', localPath);
    RealmObjectBase.set(this, 'downloadDate', downloadDate);
    RealmObjectBase.set(this, 'fileSize', fileSize);
    RealmObjectBase.set(this, 'isDownloaded', isDownloaded);
    RealmObjectBase.set(this, 'lastAccessDate', lastAccessDate);
  }

  DownloadedFileModel._();

  @override
  String get id => RealmObjectBase.get<String>(this, 'id') as String;
  @override
  set id(String value) => RealmObjectBase.set(this, 'id', value);

  @override
  String get fileType =>
      RealmObjectBase.get<String>(this, 'fileType') as String;
  @override
  set fileType(String value) => RealmObjectBase.set(this, 'fileType', value);

  @override
  String get originalUrl =>
      RealmObjectBase.get<String>(this, 'originalUrl') as String;
  @override
  set originalUrl(String value) =>
      RealmObjectBase.set(this, 'originalUrl', value);

  @override
  String get localPath =>
      RealmObjectBase.get<String>(this, 'localPath') as String;
  @override
  set localPath(String value) => RealmObjectBase.set(this, 'localPath', value);

  @override
  DateTime get downloadDate =>
      RealmObjectBase.get<DateTime>(this, 'downloadDate') as DateTime;
  @override
  set downloadDate(DateTime value) =>
      RealmObjectBase.set(this, 'downloadDate', value);

  @override
  int? get fileSize => RealmObjectBase.get<int>(this, 'fileSize') as int?;
  @override
  set fileSize(int? value) => RealmObjectBase.set(this, 'fileSize', value);

  @override
  bool get isDownloaded =>
      RealmObjectBase.get<bool>(this, 'isDownloaded') as bool;
  @override
  set isDownloaded(bool value) =>
      RealmObjectBase.set(this, 'isDownloaded', value);

  @override
  DateTime? get lastAccessDate =>
      RealmObjectBase.get<DateTime>(this, 'lastAccessDate') as DateTime?;
  @override
  set lastAccessDate(DateTime? value) =>
      RealmObjectBase.set(this, 'lastAccessDate', value);

  @override
  Stream<RealmObjectChanges<DownloadedFileModel>> get changes =>
      RealmObjectBase.getChanges<DownloadedFileModel>(this);

  @override
  Stream<RealmObjectChanges<DownloadedFileModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<DownloadedFileModel>(this, keyPaths);

  @override
  DownloadedFileModel freeze() =>
      RealmObjectBase.freezeObject<DownloadedFileModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'fileType': fileType.toEJson(),
      'originalUrl': originalUrl.toEJson(),
      'localPath': localPath.toEJson(),
      'downloadDate': downloadDate.toEJson(),
      'fileSize': fileSize.toEJson(),
      'isDownloaded': isDownloaded.toEJson(),
      'lastAccessDate': lastAccessDate.toEJson(),
    };
  }

  static EJsonValue _toEJson(DownloadedFileModel value) => value.toEJson();
  static DownloadedFileModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
        'fileType': EJsonValue fileType,
        'originalUrl': EJsonValue originalUrl,
        'localPath': EJsonValue localPath,
        'downloadDate': EJsonValue downloadDate,
        'isDownloaded': EJsonValue isDownloaded,
      } =>
        DownloadedFileModel(
          fromEJson(id),
          fromEJson(fileType),
          fromEJson(originalUrl),
          fromEJson(localPath),
          fromEJson(downloadDate),
          fromEJson(isDownloaded),
          fileSize: fromEJson(ejson['fileSize']),
          lastAccessDate: fromEJson(ejson['lastAccessDate']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(DownloadedFileModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, DownloadedFileModel, 'DownloadedFileModel', [
      SchemaProperty('id', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('fileType', RealmPropertyType.string),
      SchemaProperty('originalUrl', RealmPropertyType.string),
      SchemaProperty('localPath', RealmPropertyType.string),
      SchemaProperty('downloadDate', RealmPropertyType.timestamp),
      SchemaProperty('fileSize', RealmPropertyType.int, optional: true),
      SchemaProperty('isDownloaded', RealmPropertyType.bool),
      SchemaProperty('lastAccessDate', RealmPropertyType.timestamp,
          optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
