import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_fonts.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../../../../config/routes/app_router.gr.dart';
import '../widgets/page_indicator.dart';

@RoutePage()
class TutorialPage extends StatefulWidget {
  const TutorialPage({super.key});

  @override
  State<TutorialPage> createState() => _TutorialPageState();
}

class _TutorialPageState extends State<TutorialPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<String> _tutorialImages = [
    AppAssets.tutorial1,
    AppAssets.tutorial2,
    AppAssets.tutorial3,
    AppAssets.tutorial4,
    AppAssets.tutorial5,
    AppAssets.tutorial6,
    AppAssets.tutorial7,
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _createTutorialPageWithResourceID(String imagePath, int pageIndex) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Image.asset(
                imagePath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppColors.lightGrey2,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.image_not_supported_outlined,
                          size: 48,
                          color: AppColors.blackTint1,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tutorial Image ${pageIndex + 1}',
                          style: const TextStyle(
                            fontFamily: AppFonts.montserrat,
                            fontSize: 16,
                            color: AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          const Gap(80),
        ],
      ),
    );
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });

    // If user reaches the last page, auto-complete tutorial after a delay
    if (page == _tutorialImages.length - 1) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _completeTutorial();
        }
      });
    }
  }

  Future<void> _completeTutorial() async {
    // Disable tutorial for future logins after completion
    try {
      final dataManager = sl<DataManager>();
      await dataManager.saveTutorialEnabled(false);
    } catch (e) {
      // Handle error silently
    }

    if (mounted) {
      context.router.replaceAll([const HomeRoute()]);
    }
  }

  Future<void> _skipTutorial() async {
    // Disable tutorial for future logins after skipping
    try {
      final dataManager = sl<DataManager>();
      await dataManager.saveTutorialEnabled(false);
    } catch (e) {
      // Handle error silently
    }

    if (mounted) {
      context.router.replaceAll([const HomeRoute()]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Tutorial',
        showBackButton: false,
        hideUnderline: true,
        actions: [
          SizedBox(
            width: 60,
            height: 28,
            child: AppButton(
              text: 'Skip',
              color: AppColors.primaryBlue,
              textColor: Colors.white,
              radius: 6,
              height: 40,
              elevation: 0,
              onPressed: _skipTutorial,
            ),
          ),
          const Gap(16),
        ],
      ),
      body: Stack(
        children: [
          // Main ViewPager for tutorial images
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _tutorialImages.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 28),
                child: _createTutorialPageWithResourceID(
                  _tutorialImages[index],
                  index,
                ),
              );
            },
          ),

          // Page indicators at bottom
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: PageIndicator(
                spacing: 2,
                dotSize: 6,
                inactiveColor: Colors.transparent,
                inactiveBorderColor: AppColors.blackTint2,
                currentPage: _currentPage,
                totalPages: _tutorialImages.length,
                onPageTap: (index) {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
