import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/globals/app_globals.dart';
import 'package:storetrack_app/di/service_locator.dart';
import '../../../domain/entities/emulate_user_response_entity.dart';
import '../../../domain/usecases/get_emulate_users_usecase.dart';
import '../../../data/services/sync_service.dart';
import '../../../data/models/profile_model.dart';

part 'emulate_user_state.dart';

class EmulateUserCubit extends Cubit<EmulateUserState> {
  final GetEmulateUsersUseCase _getEmulateUsersUseCase;

  EmulateUserCubit({
    required GetEmulateUsersUseCase getEmulateUsersUseCase,
  })  : _getEmulateUsersUseCase = getEmulateUsersUseCase,
        super(EmulateUserInitial());

  Future<void> fetchEmulateUsers() async {
    emit(EmulateUserLoading());

    try {
      final dataManager = sl<DataManager>();
      const token = "Y6lI9gCoCzFdS0WdoR6v6x1vxnmstbN6";
      final userId = await dataManager.getUserId(ignoreEmulated: true);

      if (userId == null) {
        emit(const EmulateUserError(
            'Authentication error. Please login again.'));
        return;
      }

      final result = await _getEmulateUsersUseCase.call(
        token: token,
        userId: userId,
      );

      if (result.isSuccess && result.data != null) {
        final users = result.data!.data?.activeUsersInfo ?? [];
        emit(EmulateUserLoaded(
          users: users,
          filteredUsers: users,
        ));
      } else {
        emit(EmulateUserError(result.error ?? 'Failed to fetch emulate users'));
      }
    } catch (e) {
      emit(EmulateUserError('Error fetching emulate users: $e'));
    }
  }

  void searchUsers(String query) {
    final currentState = state;
    if (currentState is EmulateUserLoaded) {
      if (query.isEmpty) {
        emit(currentState.copyWith(
          filteredUsers: currentState.users,
          searchQuery: query,
        ));
      } else {
        final filteredUsers = currentState.users.where((user) {
          return user.displayName.toLowerCase().contains(query.toLowerCase()) ||
              (user.email?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (user.state?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (user.contractor?.toLowerCase().contains(query.toLowerCase()) ??
                  false);
        }).toList();

        emit(currentState.copyWith(
          filteredUsers: filteredUsers,
          searchQuery: query,
        ));
      }
    }
  }

  void clearSearch() {
    final currentState = state;
    if (currentState is EmulateUserLoaded) {
      emit(currentState.copyWith(
        filteredUsers: currentState.users,
        searchQuery: '',
      ));
    }
  }

  Future<void> emulateUser(EmulateUserEntity user) async {
    if (user.userId == null) {
      emit(const EmulateUserEmulationError('Invalid user ID'));
      return;
    }

    // Store previous state to restore after emulation
    final previousState =
        state is EmulateUserLoaded ? state as EmulateUserLoaded : null;
    final emulatedUserId = user.userId.toString();

    try {
      // Show emulation progress
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Starting emulation setup...',
        progress: 1,
      ));

      final dataManager = sl<DataManager>();
      final realmDatabase = sl<RealmDatabase>();
      final realm = realmDatabase.realm;

      // Get current user's profile before clearing
      final profileModel = realm.query<ProfileModel>('id == 0').firstOrNull;
      if (profileModel != null) {
        final firstName = profileModel.firstName ?? '';
        final lastName = profileModel.lastName ?? '';
        await dataManager.saveOriginalUserName(firstName, lastName);
      }

      // Set emulation flag, store emulated user ID globally, and clear database
      await dataManager.setEmulating(true);
      AppGlobals.setEmulatedUserId(emulatedUserId);
      realmDatabase.clearAllData();

      // Show sync progress
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Syncing essential data (tasks, history, POS)...',
        progress: 2,
      ));

      // Call the SyncService.syncForEmulation() method with the emulated user's ID
      await SyncService().sync(
        userId: emulatedUserId,
        isEmulate: true,
      );

      // Show completion progress
      emit(EmulateUserEmulating(
        user: user,
        currentApi: 'Emulation completed successfully',
        progress: 3,
      ));

      // Emulation successful - the sync service has updated all data with emulated user's info
      emit(EmulateUserEmulationSuccess(
        user: user,
        apiResults: const {
          'sync': true
        }, // Simplified result since we're using sync service
      ));

      // Brief delay to show success message, then restore user list
      await Future.delayed(const Duration(milliseconds: 1500));
      if (previousState != null) {
        emit(previousState);
      }
    } catch (e) {
      emit(EmulateUserEmulationError(
        'Error during user emulation: $e',
        user: user,
      ));

      // Restore previous state even on error
      if (previousState != null) {
        await Future.delayed(const Duration(milliseconds: 1500));
        emit(previousState);
      }
    }
  }

  void resetState() {
    emit(EmulateUserInitial());
  }

  Future<void> returnToOriginalUser() async {
    // Store previous state to restore after returning to original user
    final previousState =
        state is EmulateUserLoaded ? state as EmulateUserLoaded : null;

    try {
      emit(const EmulateUserEmulating(
        user: EmulateUserEntity(
            firstName: 'Returning', lastName: 'to original user'),
        currentApi: 'Preparing to return to original user...',
        progress: 1,
      ));

      final dataManager = sl<DataManager>();

      // Get original user ID
      final originalUserId = await dataManager.getUserId();
      if (originalUserId == null) {
        emit(const EmulateUserEmulationError('Original user ID not found'));
        if (previousState != null) {
          await Future.delayed(const Duration(milliseconds: 1500));
          emit(previousState);
        }
        return;
      }

      // Clear emulated data and sync original user's data
      emit(const EmulateUserEmulating(
        user: EmulateUserEntity(
            firstName: 'Returning', lastName: 'to original user'),
        currentApi: 'Syncing original user data...',
        progress: 2,
      ));

      final realmDatabase = sl<RealmDatabase>();
      realmDatabase.clearAllData();

      await SyncService().sync(
        userId: originalUserId,
        isEmulate: true,
      );

      // Clear emulation state
      emit(const EmulateUserEmulating(
        user: EmulateUserEntity(
            firstName: 'Returning', lastName: 'to original user'),
        currentApi: 'Completing return to original user...',
        progress: 3,
      ));

      await dataManager.clearOriginalUserData();
      AppGlobals.clearEmulatedUserId();

      // Success
      emit(const EmulateUserEmulationSuccess(
        user:
            EmulateUserEntity(firstName: 'Original', lastName: 'user restored'),
        apiResults: {'return_to_original': true},
      ));

      // Brief delay to show success message, then restore user list
      await Future.delayed(const Duration(milliseconds: 1500));
      if (previousState != null) {
        emit(previousState);
      }
    } catch (e) {
      emit(EmulateUserEmulationError('Error returning to original user: $e'));

      // Restore previous state even on error
      if (previousState != null) {
        await Future.delayed(const Duration(milliseconds: 1500));
        emit(previousState);
      }
    }
  }
}
