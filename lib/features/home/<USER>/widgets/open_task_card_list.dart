import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/accept_task_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/accept_task_request_entity.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:auto_route/auto_route.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/core/services/location_service.dart';
import 'package:location/location.dart';

class OpenTaskCardList extends StatefulWidget {
  final List<TaskDetail> tasks;
  final bool isCheckboxMode;
  final Function(List<TaskDetail> allItems, List<TaskDetail> selectedItems)?
      onSelectionChanged;
  final bool selectAll;
  final void Function(TaskDetail task)? onTaskTap;
  final bool enableTimeValidation;
  final bool disableTaskNavigation;

  const OpenTaskCardList({
    super.key,
    required this.tasks,
    this.isCheckboxMode = false,
    this.onSelectionChanged,
    this.selectAll = false,
    this.onTaskTap,
    this.enableTimeValidation = false,
    this.disableTaskNavigation = false,
  });

  @override
  State<OpenTaskCardList> createState() => _OpenTaskCardListState();
}

class _OpenTaskCardListState extends State<OpenTaskCardList> {
  final Map<String, bool> _expandedStates = {};
  final Map<String, bool> _selectedStates = {};
  final List<TaskDetail> _selectedItems = [];

  // API related fields
  late final AcceptTaskUseCase _acceptTaskUseCase;
  late final DataManager _dataManager;
  late final LocationService _locationService;
  String _userId = '';
  String _authToken = '';
  LocationData? _currentLocation;

  @override
  void initState() {
    super.initState();
    _initializeStates();
    _initializeApiDependencies();
  }

  void _initializeApiDependencies() {
    _acceptTaskUseCase = sl<AcceptTaskUseCase>();
    _dataManager = sl<DataManager>();
    _locationService = sl<LocationService>();
    _loadUserCredentials();
    _getCurrentLocation();
  }

  Future<void> _loadUserCredentials() async {
    try {
      _userId = await _dataManager.getUserId() ?? '';
      _authToken = await _dataManager.getAuthToken() ?? '';
    } catch (e) {
      // Handle credential loading error
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to load user credentials: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      _currentLocation = await _locationService.getCurrentPosition();
    } catch (e) {
      // Silently handle location error - distance will show as "N/A"
      _currentLocation = null;
    }
  }

  void _initializeStates() {
    for (var task in widget.tasks) {
      final taskId = task.taskId.toString();
      _expandedStates[taskId] = false;
      _selectedStates[taskId] = false;
    }
  }

  @override
  void didUpdateWidget(OpenTaskCardList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks) {
      _initializeStates();
    }
    if (oldWidget.selectAll != widget.selectAll) {
      _handleSelectAll(widget.selectAll);
    }
  }

  void _handleSelectAll(bool selectAll) {
    setState(() {
      for (var task in widget.tasks) {
        final taskId = task.taskId.toString();
        _selectedStates[taskId] = selectAll;
      }
      _updateSelectedItems();
    });
  }

  void _toggleExpanded(String taskId) {
    setState(() {
      _expandedStates[taskId] = !(_expandedStates[taskId] ?? false);
    });
  }

  void _toggleSelected(String taskId) {
    setState(() {
      _selectedStates[taskId] = !(_selectedStates[taskId] ?? false);
      _updateSelectedItems();
    });
  }

  void _updateSelectedItems() {
    _selectedItems.clear();
    for (var task in widget.tasks) {
      final taskId = task.taskId.toString();
      if (_selectedStates[taskId] == true) {
        _selectedItems.add(task);
      }
    }
    widget.onSelectionChanged?.call(widget.tasks, _selectedItems);
  }

  // Handle Full Brief button tap
  Future<void> _handleFullBriefTap(TaskDetail task) async {
    try {
      // Navigate to the new Full Brief page
      if (mounted) {
        context.router.push(FullBriefRoute(
          taskId: task.taskId.toString(),
        ));
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to open brief: ${e.toString()}',
        );
      }
    }
  }

  // Handle Accept Task button tap
  Future<void> _handleAcceptTaskTap(TaskDetail task) async {
    try {
      // Validate credentials
      if (_userId.isEmpty || _authToken.isEmpty) {
        await _loadUserCredentials();
        if (_userId.isEmpty || _authToken.isEmpty) {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'User credentials not available. Please login again.',
            );
          }
          return;
        }
      }

      // Create accept task request
      final request = AcceptTaskRequestEntity(
        token: _authToken,
        userId: _userId,
        taskId: task.taskId.toString(),
        dateScheduled: DateTime.now()
            .toIso8601String()
            .split('T')[0], // Today's date in YYYY-MM-DD format
      );

      // Call the API
      final result = await _acceptTaskUseCase(request);

      if (result.isSuccess && result.data != null) {
        if (mounted) {
          SnackBarService.success(
            context: context,
            message: result.data!.message.isNotEmpty
                ? result.data!.message
                : 'Task accepted successfully!',
          );
        }

        // Call the original onTaskTap callback if provided
        widget.onTaskTap?.call(task);

        // Remove the task from the list since it's now accepted
        if (mounted) {
          setState(() {
            widget.tasks.remove(task);
            _initializeStates(); // Reinitialize states for remaining tasks
          });
        }
      } else {
        final errorMessage =
            result.error?.toString() ?? 'Failed to accept task.';
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: errorMessage,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to accept task: ${e.toString()}',
        );
      }
    }
  }

  // Calculate distance using Haversine formula
  double _calculateDistanceInKm(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    // Convert degrees to radians
    double lat1Rad = lat1 * (pi / 180);
    double lon1Rad = lon1 * (pi / 180);
    double lat2Rad = lat2 * (pi / 180);
    double lon2Rad = lon2 * (pi / 180);

    // Calculate differences
    double dLat = lat2Rad - lat1Rad;
    double dLon = lon2Rad - lon1Rad;

    // Apply Haversine formula
    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  // Calculate distance between current location and task
  Future<String> _calculateDistance(TaskDetail task) async {
    try {
      // Get current location if not available
      if (_currentLocation?.latitude == null ||
          _currentLocation?.longitude == null) {
        await _getCurrentLocation();
      }

      // Check if we have current location after attempting to get it
      if (_currentLocation?.latitude == null ||
          _currentLocation?.longitude == null) {
        return "N/A";
      }

      // Get task coordinates - prioritize task-specific coordinates
      double? taskLat =
          task.taskLatitude?.toDouble() ?? task.latitude?.toDouble();
      double? taskLon =
          task.taskLongitude?.toDouble() ?? task.longitude?.toDouble();

      // Check if task has coordinates
      if (taskLat == null || taskLon == null) {
        return "N/A";
      }

      // Calculate distance
      double distance = _calculateDistanceInKm(
        _currentLocation!.latitude!,
        _currentLocation!.longitude!,
        taskLat,
        taskLon,
      );

      // Format distance with appropriate precision
      if (distance < 1) {
        return "${(distance * 1000).round()} M"; // Show in meters if less than 1 km
      } else {
        return "${distance.toStringAsFixed(1)} KM";
      }
    } catch (e) {
      return "N/A";
    }
  }

  // Generate a consistent color based on the store name (from StoreCard)
  Color _getColorFromName(String name) {
    if (name.isEmpty) return Colors.blueAccent.shade700;

    // Use the sum of character codes to generate a consistent hue
    int sum = 0;
    for (int i = 0; i < name.length; i++) {
      sum += name.codeUnitAt(i);
    }

    // Use the sum to generate a hue value between 0 and 360
    final hue = (sum % 360).toDouble();

    // Create a color with full saturation but lower brightness for darker colors
    return HSVColor.fromAHSV(1.0, hue, 0.85, 0.65).toColor();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.tasks.isEmpty) {
      return const Center(
        child: Text('No tasks available'),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.tasks.length,
      itemBuilder: (context, index) {
        final task = widget.tasks[index];
        final taskId = task.taskId.toString();
        final isExpanded = _expandedStates[taskId] ?? false;
        final isSelected = _selectedStates[taskId] ?? false;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius:
                  BorderRadius.circular(AppConstants.defaultBorderRadius),
              border: Border.all(
                color: AppColors.borderColor,
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // Task Header (Collapsible)
                GestureDetector(
                  onTap: () => _toggleExpanded(taskId),
                  child: _buildTaskHeader(task, taskId, isExpanded, isSelected),
                ),
                // Task Details (Expandable Content)
                if (isExpanded) _buildTaskDetails(task),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTaskHeader(
      TaskDetail task, String taskId, bool isExpanded, bool isSelected) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Checkbox/Tick Indicator
          if (widget.isCheckboxMode) ...[
            GestureDetector(
              onTap: () => _toggleSelected(taskId),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppColors.primaryBlue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? AppColors.primaryBlue : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.white,
                      )
                    : null,
              ),
            ),
            const Gap(12),
          ],

          // Main content area (matching StoreCard structure)
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 16,
                    backgroundColor: task.clientLogoUrl != null &&
                            task.clientLogoUrl!.isNotEmpty
                        ? Colors.transparent
                        : _getColorFromName(task.storeName ?? ''),
                    child: task.clientLogoUrl != null &&
                            task.clientLogoUrl!.isNotEmpty
                        ? ClipOval(
                            child: OfflineImageWidget(
                              url: task.clientLogoUrl!,
                              width: 32,
                              height: 32,
                              fit: BoxFit.cover,
                              borderRadius: 0,
                            ),
                          )
                        : Text(
                            (task.storeName?.length ?? 0) >= 2
                                ? task.storeName!.substring(0, 2).toUpperCase()
                                : task.storeName ?? '',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.storeName ?? "",
                        style: textTheme.montserratTitleExtraSmall,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      if (task.location != null)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Icon(
                              Icons.location_on_outlined,
                              size: 16,
                              color: AppColors.black,
                            ),
                            const SizedBox(width: 2),
                            Flexible(
                              child: Text(
                                task.location ?? "",
                                style: textTheme.montserratTableSmall,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Minutes and KM row - separate from main content
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 14,
                    color: AppColors.blackTint1,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${task.budget ?? 0}m',
                    style: textTheme.montserratParagraphSmall.copyWith(
                      color: AppColors.blackTint1,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.near_me,
                    size: 14,
                    color: AppColors.blackTint1,
                  ),
                  const SizedBox(width: 4),
                  FutureBuilder<String>(
                    future: _calculateDistance(task),
                    builder: (context, snapshot) {
                      return Text(
                        snapshot.data ?? "N/A",
                        style: textTheme.montserratParagraphSmall.copyWith(
                          color: AppColors.blackTint1,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // Right side controls
        ],
      ),
    );
  }

  Widget _buildTaskDetails(TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Client Name
          Text(
            task.client ?? "Client Name",
            style: textTheme.montserratTitleExtraSmall.copyWith(
              color: AppColors.black,
            ),
          ),

          const Gap(8),

          // Cycle Information
          _buildDetailRow("Cycle:", task.cycle ?? "N/A"),

          // Date Range
          _buildDetailRow(
            "Range:",
            task.rangeStart != null && task.rangeEnd != null
                ? "${DateFormat('dd/MM/yyyy').format(task.rangeStart!)} - ${DateFormat('dd/MM/yyyy').format(task.rangeEnd!)}"
                : "N/A",
          ),

          // Budget
          _buildDetailRow("Budget:", "${task.budget ?? 0}m"),

          // Expires Date
          _buildDetailRow(
            "Expires:",
            task.expires != null
                ? DateFormat('dd/MM/yyyy').format(task.expires!)
                : "N/A",
          ),

          // Re-opened Status
          _buildDetailRow(
            "Re-opened:",
            task.reOpened == true ? (task.reOpenedReason ?? "Yes") : "No",
          ),

          // Description
          _buildDetailRow(
              "Description:", task.comment ?? task.taskNote ?? "N/A"),

          const Gap(16),

          // Action Buttons
          Row(
            children: [
              // Full Brief Button
              Expanded(
                child: AppButton(
                  text: "Full Brief",
                  color: AppColors.primaryBlue,
                  onPressed: () => _handleFullBriefTap(task),
                  height: 40,
                  radius: 4,
                  elevation: 0,
                ),
              ),

              const Gap(10),

              // Accept Task Button
              Expanded(
                child: AppButton(
                  text: "Accept Task",
                  color: AppColors.loginGreen,
                  onPressed: () => _handleAcceptTaskTap(task),
                  height: 40,
                  radius: 4,
                  elevation: 0,
                ),
              ),

              if (task.taskStatus != "Tentative") const Gap(10),

              // Reject Task Button (hidden for open tasks)
              if (task.taskStatus != "Tentative")
                Expanded(
                  child: AppButton(
                    text: "Reject",
                    color: AppColors.loginRed,
                    onPressed: () {
                      // Handle reject task action
                    },
                    height: 40,
                    radius: 4,
                    elevation: 0,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool showLock = false}) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: textTheme.montserratTableSmall.copyWith(
                color: AppColors.blackTint1,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (showLock) ...[
            const Icon(
              Icons.lock,
              size: 16,
              color: Colors.grey,
            ),
            const Gap(4),
          ],
          Expanded(
            child: Text(
              value,
              style: textTheme.montserratTableSmall.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
