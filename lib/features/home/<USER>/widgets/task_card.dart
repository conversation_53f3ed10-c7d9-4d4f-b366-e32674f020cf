import 'package:flutter/material.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_constants.dart';

class TaskCard extends StatefulWidget {
  final Widget content;
  final List<Widget> actions;
  final double borderRadius;
  final Color backgroundColor;
  final Border? border;
  final EdgeInsets padding;
  // Add callbacks for drag events if needed by parent widgets
  final ValueChanged<DragUpdateDetails>? onHorizontalDragUpdate;
  final ValueChanged<DragEndDetails>? onHorizontalDragEnd;
  final bool isCalendarMode;
  final bool initialCheckboxValue;
  final ValueChanged<bool>? onCheckboxChanged;
  final double minSwipeWidth;
  final double actionSpacing;
  final bool showScheduledDate;
  final bool showTickIndicator;
  final bool showAllDisclosureIndicator;
  final bool permanentlyDisableAllDisclosureIndicator;
  final bool isOpenTask;
  final MainAxisAlignment actionsAlignment;
  final bool isParentCard;

  const TaskCard({
    super.key,
    required this.content,
    this.actions = const [],
    this.borderRadius = AppConstants.defaultBorderRadius,
    this.backgroundColor = Colors.white,
    this.border,
    this.padding = const EdgeInsets.all(AppConstants.defaultPadding),
    this.onHorizontalDragUpdate, // Optional callback
    this.onHorizontalDragEnd, // Optional callback
    this.isCalendarMode = false,
    this.initialCheckboxValue = false,
    this.onCheckboxChanged,
    this.minSwipeWidth = 120.0, // Minimum width for swipe options
    this.actionSpacing = 8.0, // Spacing between action buttons
    this.showScheduledDate = false,
    this.showTickIndicator = false,
    this.showAllDisclosureIndicator = false,
    this.permanentlyDisableAllDisclosureIndicator = false,
    this.isOpenTask = false,
    this.actionsAlignment = MainAxisAlignment.center,
    this.isParentCard = false,
  });

  @override
  TaskCardState createState() => TaskCardState();
}

class TaskCardState extends State<TaskCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  // Calculate total width based on number of actions
  double get _optionsTotalWidth {
    if (widget.actions.isEmpty) return 0;

    // Calculate width based on number of actions
    // Each TaskActionButton is 32px wide
    double actionButtonWidth = 32.0; // Base width of TaskActionButton

    // Determine if we need to display in two rows
    bool useDoubleRow = widget.actions.length > 3;

    // Calculate number of buttons per row
    int buttonsInFirstRow = useDoubleRow
        ? (widget.actions.length / 2).ceil()
        : widget.actions.length;
    int buttonsInSecondRow =
        useDoubleRow ? widget.actions.length - buttonsInFirstRow : 0;

    // Calculate max buttons in a row
    int maxButtonsInRow = buttonsInFirstRow > buttonsInSecondRow
        ? buttonsInFirstRow
        : buttonsInSecondRow;

    // Calculate width for buttons in a row
    double totalActionWidth = maxButtonsInRow * actionButtonWidth;

    // Add spacing between buttons (n-1 spaces for n buttons in a row)
    double totalSpacingWidth =
        maxButtonsInRow > 1 ? (maxButtonsInRow - 1) * widget.actionSpacing : 0;

    // Add padding on left side only (no padding on right)
    double totalPadding = widget.actionSpacing * 2.0;

    // Calculate total width
    return totalActionWidth + totalSpacingWidth + totalPadding;
  }

  // For spring effect tracking (used in _handleDragUpdate)

  // State for checkbox
  bool _isChecked = false;

  // Animation parameters
  final Curve _animationCurve = Curves.easeOut;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(
          milliseconds: 250), // Shorter duration for snappier effect
      vsync: this,
    );

    // Use a standard curve for snapping animation
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: _animationCurve,
      ),
    );

    // Initialize checkbox state
    _isChecked = widget.initialCheckboxValue;
  }

  @override
  void didUpdateWidget(TaskCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update checkbox state if initialCheckboxValue changes
    if (oldWidget.initialCheckboxValue != widget.initialCheckboxValue) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isChecked = widget.initialCheckboxValue;
        });
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    // Allow parent widget to handle drag if callback is provided
    if (widget.onHorizontalDragUpdate != null) {
      widget.onHorizontalDragUpdate!(details);
      return; // Parent handles the state
    }

    // Default handling if no callback is provided
    if (widget.actions.isEmpty || _optionsTotalWidth <= 0) return;

    // Get the delta and update drag position
    double delta = details.primaryDelta ?? 0;

    // Add a slight resistance effect as the card approaches fully open/closed
    // This creates a more natural feel
    double resistance = 1.0;
    if (_controller.value > 0.8 && delta < 0) {
      // More resistance as we approach fully open
      resistance = 0.5;
    } else if (_controller.value < 0.2 && delta > 0) {
      // More resistance as we approach fully closed
      resistance = 0.5;
    }

    // Calculate drag percentage based on the dynamic total width
    double totalWidth = _optionsTotalWidth;
    double dragPercent = (delta * resistance).abs() / totalWidth;

    // DragUpdateDetails doesn't have velocity, we'll capture it in drag end

    // Swiping left (revealing actions)
    if (delta < 0) {
      _controller.value = (_controller.value + dragPercent).clamp(0.0, 1.0);
    }
    // Swiping right (hiding actions)
    else if (delta > 0) {
      _controller.value = (_controller.value - dragPercent).clamp(0.0, 1.0);
    }

    // Controller value is already updated for animation
  }

  void _handleDragEnd(DragEndDetails details) {
    // Allow parent widget to handle drag end if callback is provided
    if (widget.onHorizontalDragEnd != null) {
      widget.onHorizontalDragEnd!(details);
      return; // Parent handles the state
    }

    // Default handling if no callback is provided
    if (widget.actions.isEmpty || _optionsTotalWidth <= 0) return;

    // Get the fling velocity for determining direction
    double flingVelocity = details.velocity.pixelsPerSecond.dx;

    // Determine target based on velocity and current position
    double target;

    // Use a threshold to determine whether to snap open or closed
    double positionThreshold = 0.3; // Default threshold

    // Adjust threshold based on velocity - faster swipes need less distance
    if (flingVelocity.abs() > 800) {
      positionThreshold = 0.15;
    }

    // Adjust threshold based on number of actions
    // For fewer actions, make it easier to open
    if (widget.actions.length <= 2) {
      positionThreshold *= 0.8; // Reduce threshold for fewer actions
    }

    if (flingVelocity < -500) {
      // Fast swipe left - open
      target = 1.0;
    } else if (flingVelocity > 500) {
      // Fast swipe right - close
      target = 0.0;
    } else if (_controller.value >= positionThreshold) {
      // Slower movement but past threshold - open
      target = 1.0;
    } else {
      // Default - close
      target = 0.0;
    }

    // Animate to the target value with a standard animation
    if (target == 1.0) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCalendarMode) {
      return LayoutBuilder(
        builder: (context, constraints) {
          return Padding(
            padding: const EdgeInsets.symmetric(
                vertical: 6.0), // Consistent padding with non-calendar mode
            child: Row(
              crossAxisAlignment: widget.isParentCard
                  ? CrossAxisAlignment.start
                  : CrossAxisAlignment.center,
              children: [
                // Main content
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: widget.backgroundColor,
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      border: widget.border,
                    ),
                    child: Padding(
                      padding: widget.padding,
                      child: widget.content,
                    ),
                  ),
                ),

                // Checkbox with appropriate alignment
                Padding(
                  padding: EdgeInsets.only(
                    left: 14, // Adjusted to 14 pixels as requested
                    right: 0, // No right padding
                    // Add top padding only for parent cards
                    top: widget.isParentCard ? 20 : 0,
                  ),
                  child: GestureDetector(
                    onTap: () {
                      // Direct toggle approach
                      final newValue = !_isChecked;
                      setState(() {
                        _isChecked = newValue;
                      });

                      // Notify callback directly
                      if (widget.onCheckboxChanged != null) {
                        widget.onCheckboxChanged!(newValue);
                      }
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color:
                            _isChecked ? AppColors.primaryBlue : Colors.white,
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(
                          color: _isChecked
                              ? AppColors.primaryBlue
                              : AppColors.blackTint2,
                          width: 1.5,
                        ),
                      ),
                      child: _isChecked
                          ? const Icon(
                              Icons.check,
                              size: 18,
                              color: Colors.white,
                            )
                          : null,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    // Original swipeable implementation for non-calendar mode
    final dragUpdateHandler =
        widget.onHorizontalDragUpdate ?? _handleDragUpdate;
    final dragEndHandler = widget.onHorizontalDragEnd ?? _handleDragEnd;

    return GestureDetector(
      onHorizontalDragUpdate:
          widget.actions.isNotEmpty ? dragUpdateHandler : null,
      onHorizontalDragEnd: widget.actions.isNotEmpty ? dragEndHandler : null,
      behavior: HitTestBehavior.opaque,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final maxWidth = constraints.maxWidth;

          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final double revealedActionsWidth =
                  _optionsTotalWidth * _animation.value;
              final double cardContentWidth =
                  (maxWidth - revealedActionsWidth).clamp(0.0, maxWidth);

              // No need to adjust card width in non-calendar mode

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6.0),
                child: Stack(
                  children: [
                    // Action buttons positioned on the right
                    Positioned(
                      top: 0,
                      bottom: 0,
                      right: 0,
                      width: revealedActionsWidth,
                      child: Container(
                        padding: EdgeInsets.zero,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                        ),
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          child: Opacity(
                            opacity: _animation.value.clamp(0.5, 1.0),
                            child: Container(
                              height: double.infinity,
                              padding: EdgeInsets.only(
                                top: 0,
                                left: widget.actionSpacing * 2.0,
                                right: 0,
                                bottom: 0,
                              ),
                              child: Column(
                                mainAxisAlignment: widget.actionsAlignment,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                      height: widget.actionsAlignment ==
                                              MainAxisAlignment.start
                                          ? 20 // 20dp gap at top for top alignment
                                          : widget.actionSpacing),
                                  LayoutBuilder(
                                    builder: (context, constraints) {
                                      // Determine if we need to display in two rows
                                      bool useDoubleRow =
                                          widget.actions.length > 3;

                                      if (useDoubleRow) {
                                        // Calculate number of buttons per row
                                        int buttonsInFirstRow =
                                            (widget.actions.length / 2).ceil();

                                        // Create two rows of buttons
                                        return Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              widget.actionsAlignment,
                                          children: [
                                            // First row
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: List.generate(
                                                buttonsInFirstRow,
                                                (index) => Padding(
                                                  padding: EdgeInsets.only(
                                                    right: index <
                                                            buttonsInFirstRow -
                                                                1
                                                        ? widget.actionSpacing
                                                        : 0,
                                                  ),
                                                  child: widget.actions[index],
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                                height: widget.actionSpacing),
                                            // Second row
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: List.generate(
                                                widget.actions.length -
                                                    buttonsInFirstRow,
                                                (index) {
                                                  final actualIndex =
                                                      index + buttonsInFirstRow;
                                                  return Padding(
                                                    padding: EdgeInsets.only(
                                                      right: index <
                                                              (widget.actions
                                                                      .length -
                                                                  buttonsInFirstRow -
                                                                  1)
                                                          ? widget.actionSpacing
                                                          : 0,
                                                    ),
                                                    child: widget
                                                        .actions[actualIndex],
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        );
                                      } else {
                                        // Single row layout - wrap in a Column to respect alignment
                                        return Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              widget.actionsAlignment,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: List.generate(
                                                widget.actions.length,
                                                (index) => Padding(
                                                  padding: EdgeInsets.only(
                                                    right: index <
                                                            widget.actions
                                                                    .length -
                                                                1
                                                        ? widget.actionSpacing
                                                        : 0,
                                                  ),
                                                  child: widget.actions[index],
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // No checkbox in non-calendar mode

                    // Main card content
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        width: cardContentWidth,
                        margin: null,
                        decoration: BoxDecoration(
                          color: widget.backgroundColor,
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          border: widget.border,
                        ),
                        child: ClipRRect(
                          borderRadius:
                              BorderRadius.circular(widget.borderRadius),
                          child: SingleChildScrollView(
                            physics: const NeverScrollableScrollPhysics(),
                            child: Padding(
                              padding: widget.padding,
                              child: widget.content,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
