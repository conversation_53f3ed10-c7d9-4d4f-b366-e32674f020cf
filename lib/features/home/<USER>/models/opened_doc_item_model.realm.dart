// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'opened_doc_item_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class OpenedDocItemModel extends _OpenedDocItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  OpenedDocItemModel(
    String docId,
    String timeStamp,
  ) {
    RealmObjectBase.set(this, 'docId', docId);
    RealmObjectBase.set(this, 'timeStamp', timeStamp);
  }

  OpenedDocItemModel._();

  @override
  String get docId => RealmObjectBase.get<String>(this, 'docId') as String;
  @override
  set docId(String value) => RealmObjectBase.set(this, 'docId', value);

  @override
  String get timeStamp =>
      RealmObjectBase.get<String>(this, 'timeStamp') as String;
  @override
  set timeStamp(String value) => RealmObjectBase.set(this, 'timeStamp', value);

  @override
  Stream<RealmObjectChanges<OpenedDocItemModel>> get changes =>
      RealmObjectBase.getChanges<OpenedDocItemModel>(this);

  @override
  Stream<RealmObjectChanges<OpenedDocItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<OpenedDocItemModel>(this, keyPaths);

  @override
  OpenedDocItemModel freeze() =>
      RealmObjectBase.freezeObject<OpenedDocItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'docId': docId.toEJson(),
      'timeStamp': timeStamp.toEJson(),
    };
  }

  static EJsonValue _toEJson(OpenedDocItemModel value) => value.toEJson();
  static OpenedDocItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'docId': EJsonValue docId,
        'timeStamp': EJsonValue timeStamp,
      } =>
        OpenedDocItemModel(
          fromEJson(docId),
          fromEJson(timeStamp),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(OpenedDocItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, OpenedDocItemModel, 'OpenedDocItemModel', [
      SchemaProperty('docId', RealmPropertyType.string, primaryKey: true),
      SchemaProperty('timeStamp', RealmPropertyType.string),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
