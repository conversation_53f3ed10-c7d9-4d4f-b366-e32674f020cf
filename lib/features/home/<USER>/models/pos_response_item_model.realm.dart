// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pos_response_item_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class PosResponseItemModel extends _PosResponseItemModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  PosResponseItemModel(
    int id, {
    bool isSynced = false,
    int? taskId,
    String? storeName,
    String? clientName,
    String? cycle,
    DateTime? rangeStart,
    DateTime? rangeEnd,
    DateTime? scheduledDate,
    String? received,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<PosResponseItemModel>({
        'id': 0,
        'isSynced': false,
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'isSynced', isSynced);
    RealmObjectBase.set(this, 'taskId', taskId);
    RealmObjectBase.set(this, 'storeName', storeName);
    RealmObjectBase.set(this, 'clientName', clientName);
    RealmObjectBase.set(this, 'cycle', cycle);
    RealmObjectBase.set(this, 'rangeStart', rangeStart);
    RealmObjectBase.set(this, 'rangeEnd', rangeEnd);
    RealmObjectBase.set(this, 'scheduledDate', scheduledDate);
    RealmObjectBase.set(this, 'received', received);
    RealmObjectBase.set(this, 'createdAt', createdAt);
    RealmObjectBase.set(this, 'updatedAt', updatedAt);
  }

  PosResponseItemModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  bool get isSynced => RealmObjectBase.get<bool>(this, 'isSynced') as bool;
  @override
  set isSynced(bool value) => RealmObjectBase.set(this, 'isSynced', value);

  @override
  int? get taskId => RealmObjectBase.get<int>(this, 'taskId') as int?;
  @override
  set taskId(int? value) => RealmObjectBase.set(this, 'taskId', value);

  @override
  String? get storeName =>
      RealmObjectBase.get<String>(this, 'storeName') as String?;
  @override
  set storeName(String? value) => RealmObjectBase.set(this, 'storeName', value);

  @override
  String? get clientName =>
      RealmObjectBase.get<String>(this, 'clientName') as String?;
  @override
  set clientName(String? value) =>
      RealmObjectBase.set(this, 'clientName', value);

  @override
  String? get cycle => RealmObjectBase.get<String>(this, 'cycle') as String?;
  @override
  set cycle(String? value) => RealmObjectBase.set(this, 'cycle', value);

  @override
  DateTime? get rangeStart =>
      RealmObjectBase.get<DateTime>(this, 'rangeStart') as DateTime?;
  @override
  set rangeStart(DateTime? value) =>
      RealmObjectBase.set(this, 'rangeStart', value);

  @override
  DateTime? get rangeEnd =>
      RealmObjectBase.get<DateTime>(this, 'rangeEnd') as DateTime?;
  @override
  set rangeEnd(DateTime? value) => RealmObjectBase.set(this, 'rangeEnd', value);

  @override
  DateTime? get scheduledDate =>
      RealmObjectBase.get<DateTime>(this, 'scheduledDate') as DateTime?;
  @override
  set scheduledDate(DateTime? value) =>
      RealmObjectBase.set(this, 'scheduledDate', value);

  @override
  String? get received =>
      RealmObjectBase.get<String>(this, 'received') as String?;
  @override
  set received(String? value) => RealmObjectBase.set(this, 'received', value);

  @override
  DateTime? get createdAt =>
      RealmObjectBase.get<DateTime>(this, 'createdAt') as DateTime?;
  @override
  set createdAt(DateTime? value) =>
      RealmObjectBase.set(this, 'createdAt', value);

  @override
  DateTime? get updatedAt =>
      RealmObjectBase.get<DateTime>(this, 'updatedAt') as DateTime?;
  @override
  set updatedAt(DateTime? value) =>
      RealmObjectBase.set(this, 'updatedAt', value);

  @override
  Stream<RealmObjectChanges<PosResponseItemModel>> get changes =>
      RealmObjectBase.getChanges<PosResponseItemModel>(this);

  @override
  Stream<RealmObjectChanges<PosResponseItemModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<PosResponseItemModel>(this, keyPaths);

  @override
  PosResponseItemModel freeze() =>
      RealmObjectBase.freezeObject<PosResponseItemModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'isSynced': isSynced.toEJson(),
      'taskId': taskId.toEJson(),
      'storeName': storeName.toEJson(),
      'clientName': clientName.toEJson(),
      'cycle': cycle.toEJson(),
      'rangeStart': rangeStart.toEJson(),
      'rangeEnd': rangeEnd.toEJson(),
      'scheduledDate': scheduledDate.toEJson(),
      'received': received.toEJson(),
      'createdAt': createdAt.toEJson(),
      'updatedAt': updatedAt.toEJson(),
    };
  }

  static EJsonValue _toEJson(PosResponseItemModel value) => value.toEJson();
  static PosResponseItemModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        PosResponseItemModel(
          fromEJson(ejson['id'], defaultValue: 0),
          isSynced: fromEJson(ejson['isSynced'], defaultValue: false),
          taskId: fromEJson(ejson['taskId']),
          storeName: fromEJson(ejson['storeName']),
          clientName: fromEJson(ejson['clientName']),
          cycle: fromEJson(ejson['cycle']),
          rangeStart: fromEJson(ejson['rangeStart']),
          rangeEnd: fromEJson(ejson['rangeEnd']),
          scheduledDate: fromEJson(ejson['scheduledDate']),
          received: fromEJson(ejson['received']),
          createdAt: fromEJson(ejson['createdAt']),
          updatedAt: fromEJson(ejson['updatedAt']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(PosResponseItemModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, PosResponseItemModel, 'PosResponseItemModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('isSynced', RealmPropertyType.bool),
      SchemaProperty('taskId', RealmPropertyType.int, optional: true),
      SchemaProperty('storeName', RealmPropertyType.string, optional: true),
      SchemaProperty('clientName', RealmPropertyType.string, optional: true),
      SchemaProperty('cycle', RealmPropertyType.string, optional: true),
      SchemaProperty('rangeStart', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('rangeEnd', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('scheduledDate', RealmPropertyType.timestamp,
          optional: true),
      SchemaProperty('received', RealmPropertyType.string, optional: true),
      SchemaProperty('createdAt', RealmPropertyType.timestamp, optional: true),
      SchemaProperty('updatedAt', RealmPropertyType.timestamp, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
