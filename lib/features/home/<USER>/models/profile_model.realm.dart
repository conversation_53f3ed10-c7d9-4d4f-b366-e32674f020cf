// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_model.dart';

// **************************************************************************
// RealmObjectGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
class ProfileModel extends _ProfileModel
    with RealmEntity, RealmObjectBase, RealmObject {
  static var _defaultsSet = false;

  ProfileModel(
    int id, {
    String? token,
    int? userId,
    int? contractorId,
    int? countryId,
    int? stateId,
    String? firstName,
    String? lastName,
    String? address,
    String? email,
    String? country,
    String? state,
    String? suburb,
    String? postcode,
    String? pAddress,
    String? pSuburb,
    String? pPostcode,
    int? pCountryId,
    String? pCountry,
    String? pRegion,
    int? pRegionId,
    String? pDeliveryComment,
    String? mobile,
    String? profileImageUrl,
    String? modifiedTimeStampProfile,
    bool? adminAccess,
    bool? createTask,
    String? orgIDs,
  }) {
    if (!_defaultsSet) {
      _defaultsSet = RealmObjectBase.setDefaults<ProfileModel>({
        'id': 0,
      });
    }
    RealmObjectBase.set(this, 'id', id);
    RealmObjectBase.set(this, 'token', token);
    RealmObjectBase.set(this, 'userId', userId);
    RealmObjectBase.set(this, 'contractorId', contractorId);
    RealmObjectBase.set(this, 'countryId', countryId);
    RealmObjectBase.set(this, 'stateId', stateId);
    RealmObjectBase.set(this, 'firstName', firstName);
    RealmObjectBase.set(this, 'lastName', lastName);
    RealmObjectBase.set(this, 'address', address);
    RealmObjectBase.set(this, 'email', email);
    RealmObjectBase.set(this, 'country', country);
    RealmObjectBase.set(this, 'state', state);
    RealmObjectBase.set(this, 'suburb', suburb);
    RealmObjectBase.set(this, 'postcode', postcode);
    RealmObjectBase.set(this, 'pAddress', pAddress);
    RealmObjectBase.set(this, 'pSuburb', pSuburb);
    RealmObjectBase.set(this, 'pPostcode', pPostcode);
    RealmObjectBase.set(this, 'pCountryId', pCountryId);
    RealmObjectBase.set(this, 'pCountry', pCountry);
    RealmObjectBase.set(this, 'pRegion', pRegion);
    RealmObjectBase.set(this, 'pRegionId', pRegionId);
    RealmObjectBase.set(this, 'pDeliveryComment', pDeliveryComment);
    RealmObjectBase.set(this, 'mobile', mobile);
    RealmObjectBase.set(this, 'profileImageUrl', profileImageUrl);
    RealmObjectBase.set(
        this, 'modifiedTimeStampProfile', modifiedTimeStampProfile);
    RealmObjectBase.set(this, 'adminAccess', adminAccess);
    RealmObjectBase.set(this, 'createTask', createTask);
    RealmObjectBase.set(this, 'orgIDs', orgIDs);
  }

  ProfileModel._();

  @override
  int get id => RealmObjectBase.get<int>(this, 'id') as int;
  @override
  set id(int value) => RealmObjectBase.set(this, 'id', value);

  @override
  String? get token => RealmObjectBase.get<String>(this, 'token') as String?;
  @override
  set token(String? value) => RealmObjectBase.set(this, 'token', value);

  @override
  int? get userId => RealmObjectBase.get<int>(this, 'userId') as int?;
  @override
  set userId(int? value) => RealmObjectBase.set(this, 'userId', value);

  @override
  int? get contractorId =>
      RealmObjectBase.get<int>(this, 'contractorId') as int?;
  @override
  set contractorId(int? value) =>
      RealmObjectBase.set(this, 'contractorId', value);

  @override
  int? get countryId => RealmObjectBase.get<int>(this, 'countryId') as int?;
  @override
  set countryId(int? value) => RealmObjectBase.set(this, 'countryId', value);

  @override
  int? get stateId => RealmObjectBase.get<int>(this, 'stateId') as int?;
  @override
  set stateId(int? value) => RealmObjectBase.set(this, 'stateId', value);

  @override
  String? get firstName =>
      RealmObjectBase.get<String>(this, 'firstName') as String?;
  @override
  set firstName(String? value) => RealmObjectBase.set(this, 'firstName', value);

  @override
  String? get lastName =>
      RealmObjectBase.get<String>(this, 'lastName') as String?;
  @override
  set lastName(String? value) => RealmObjectBase.set(this, 'lastName', value);

  @override
  String? get address =>
      RealmObjectBase.get<String>(this, 'address') as String?;
  @override
  set address(String? value) => RealmObjectBase.set(this, 'address', value);

  @override
  String? get email => RealmObjectBase.get<String>(this, 'email') as String?;
  @override
  set email(String? value) => RealmObjectBase.set(this, 'email', value);

  @override
  String? get country =>
      RealmObjectBase.get<String>(this, 'country') as String?;
  @override
  set country(String? value) => RealmObjectBase.set(this, 'country', value);

  @override
  String? get state => RealmObjectBase.get<String>(this, 'state') as String?;
  @override
  set state(String? value) => RealmObjectBase.set(this, 'state', value);

  @override
  String? get suburb => RealmObjectBase.get<String>(this, 'suburb') as String?;
  @override
  set suburb(String? value) => RealmObjectBase.set(this, 'suburb', value);

  @override
  String? get postcode =>
      RealmObjectBase.get<String>(this, 'postcode') as String?;
  @override
  set postcode(String? value) => RealmObjectBase.set(this, 'postcode', value);

  @override
  String? get pAddress =>
      RealmObjectBase.get<String>(this, 'pAddress') as String?;
  @override
  set pAddress(String? value) => RealmObjectBase.set(this, 'pAddress', value);

  @override
  String? get pSuburb =>
      RealmObjectBase.get<String>(this, 'pSuburb') as String?;
  @override
  set pSuburb(String? value) => RealmObjectBase.set(this, 'pSuburb', value);

  @override
  String? get pPostcode =>
      RealmObjectBase.get<String>(this, 'pPostcode') as String?;
  @override
  set pPostcode(String? value) => RealmObjectBase.set(this, 'pPostcode', value);

  @override
  int? get pCountryId => RealmObjectBase.get<int>(this, 'pCountryId') as int?;
  @override
  set pCountryId(int? value) => RealmObjectBase.set(this, 'pCountryId', value);

  @override
  String? get pCountry =>
      RealmObjectBase.get<String>(this, 'pCountry') as String?;
  @override
  set pCountry(String? value) => RealmObjectBase.set(this, 'pCountry', value);

  @override
  String? get pRegion =>
      RealmObjectBase.get<String>(this, 'pRegion') as String?;
  @override
  set pRegion(String? value) => RealmObjectBase.set(this, 'pRegion', value);

  @override
  int? get pRegionId => RealmObjectBase.get<int>(this, 'pRegionId') as int?;
  @override
  set pRegionId(int? value) => RealmObjectBase.set(this, 'pRegionId', value);

  @override
  String? get pDeliveryComment =>
      RealmObjectBase.get<String>(this, 'pDeliveryComment') as String?;
  @override
  set pDeliveryComment(String? value) =>
      RealmObjectBase.set(this, 'pDeliveryComment', value);

  @override
  String? get mobile => RealmObjectBase.get<String>(this, 'mobile') as String?;
  @override
  set mobile(String? value) => RealmObjectBase.set(this, 'mobile', value);

  @override
  String? get profileImageUrl =>
      RealmObjectBase.get<String>(this, 'profileImageUrl') as String?;
  @override
  set profileImageUrl(String? value) =>
      RealmObjectBase.set(this, 'profileImageUrl', value);

  @override
  String? get modifiedTimeStampProfile =>
      RealmObjectBase.get<String>(this, 'modifiedTimeStampProfile') as String?;
  @override
  set modifiedTimeStampProfile(String? value) =>
      RealmObjectBase.set(this, 'modifiedTimeStampProfile', value);

  @override
  bool? get adminAccess =>
      RealmObjectBase.get<bool>(this, 'adminAccess') as bool?;
  @override
  set adminAccess(bool? value) =>
      RealmObjectBase.set(this, 'adminAccess', value);

  @override
  bool? get createTask =>
      RealmObjectBase.get<bool>(this, 'createTask') as bool?;
  @override
  set createTask(bool? value) => RealmObjectBase.set(this, 'createTask', value);

  @override
  String? get orgIDs => RealmObjectBase.get<String>(this, 'orgIDs') as String?;
  @override
  set orgIDs(String? value) => RealmObjectBase.set(this, 'orgIDs', value);

  @override
  Stream<RealmObjectChanges<ProfileModel>> get changes =>
      RealmObjectBase.getChanges<ProfileModel>(this);

  @override
  Stream<RealmObjectChanges<ProfileModel>> changesFor(
          [List<String>? keyPaths]) =>
      RealmObjectBase.getChangesFor<ProfileModel>(this, keyPaths);

  @override
  ProfileModel freeze() => RealmObjectBase.freezeObject<ProfileModel>(this);

  EJsonValue toEJson() {
    return <String, dynamic>{
      'id': id.toEJson(),
      'token': token.toEJson(),
      'userId': userId.toEJson(),
      'contractorId': contractorId.toEJson(),
      'countryId': countryId.toEJson(),
      'stateId': stateId.toEJson(),
      'firstName': firstName.toEJson(),
      'lastName': lastName.toEJson(),
      'address': address.toEJson(),
      'email': email.toEJson(),
      'country': country.toEJson(),
      'state': state.toEJson(),
      'suburb': suburb.toEJson(),
      'postcode': postcode.toEJson(),
      'pAddress': pAddress.toEJson(),
      'pSuburb': pSuburb.toEJson(),
      'pPostcode': pPostcode.toEJson(),
      'pCountryId': pCountryId.toEJson(),
      'pCountry': pCountry.toEJson(),
      'pRegion': pRegion.toEJson(),
      'pRegionId': pRegionId.toEJson(),
      'pDeliveryComment': pDeliveryComment.toEJson(),
      'mobile': mobile.toEJson(),
      'profileImageUrl': profileImageUrl.toEJson(),
      'modifiedTimeStampProfile': modifiedTimeStampProfile.toEJson(),
      'adminAccess': adminAccess.toEJson(),
      'createTask': createTask.toEJson(),
      'orgIDs': orgIDs.toEJson(),
    };
  }

  static EJsonValue _toEJson(ProfileModel value) => value.toEJson();
  static ProfileModel _fromEJson(EJsonValue ejson) {
    if (ejson is! Map<String, dynamic>) return raiseInvalidEJson(ejson);
    return switch (ejson) {
      {
        'id': EJsonValue id,
      } =>
        ProfileModel(
          fromEJson(ejson['id'], defaultValue: 0),
          token: fromEJson(ejson['token']),
          userId: fromEJson(ejson['userId']),
          contractorId: fromEJson(ejson['contractorId']),
          countryId: fromEJson(ejson['countryId']),
          stateId: fromEJson(ejson['stateId']),
          firstName: fromEJson(ejson['firstName']),
          lastName: fromEJson(ejson['lastName']),
          address: fromEJson(ejson['address']),
          email: fromEJson(ejson['email']),
          country: fromEJson(ejson['country']),
          state: fromEJson(ejson['state']),
          suburb: fromEJson(ejson['suburb']),
          postcode: fromEJson(ejson['postcode']),
          pAddress: fromEJson(ejson['pAddress']),
          pSuburb: fromEJson(ejson['pSuburb']),
          pPostcode: fromEJson(ejson['pPostcode']),
          pCountryId: fromEJson(ejson['pCountryId']),
          pCountry: fromEJson(ejson['pCountry']),
          pRegion: fromEJson(ejson['pRegion']),
          pRegionId: fromEJson(ejson['pRegionId']),
          pDeliveryComment: fromEJson(ejson['pDeliveryComment']),
          mobile: fromEJson(ejson['mobile']),
          profileImageUrl: fromEJson(ejson['profileImageUrl']),
          modifiedTimeStampProfile:
              fromEJson(ejson['modifiedTimeStampProfile']),
          adminAccess: fromEJson(ejson['adminAccess']),
          createTask: fromEJson(ejson['createTask']),
          orgIDs: fromEJson(ejson['orgIDs']),
        ),
      _ => raiseInvalidEJson(ejson),
    };
  }

  static final schema = () {
    RealmObjectBase.registerFactory(ProfileModel._);
    register(_toEJson, _fromEJson);
    return const SchemaObject(
        ObjectType.realmObject, ProfileModel, 'ProfileModel', [
      SchemaProperty('id', RealmPropertyType.int, primaryKey: true),
      SchemaProperty('token', RealmPropertyType.string, optional: true),
      SchemaProperty('userId', RealmPropertyType.int, optional: true),
      SchemaProperty('contractorId', RealmPropertyType.int, optional: true),
      SchemaProperty('countryId', RealmPropertyType.int, optional: true),
      SchemaProperty('stateId', RealmPropertyType.int, optional: true),
      SchemaProperty('firstName', RealmPropertyType.string, optional: true),
      SchemaProperty('lastName', RealmPropertyType.string, optional: true),
      SchemaProperty('address', RealmPropertyType.string, optional: true),
      SchemaProperty('email', RealmPropertyType.string, optional: true),
      SchemaProperty('country', RealmPropertyType.string, optional: true),
      SchemaProperty('state', RealmPropertyType.string, optional: true),
      SchemaProperty('suburb', RealmPropertyType.string, optional: true),
      SchemaProperty('postcode', RealmPropertyType.string, optional: true),
      SchemaProperty('pAddress', RealmPropertyType.string, optional: true),
      SchemaProperty('pSuburb', RealmPropertyType.string, optional: true),
      SchemaProperty('pPostcode', RealmPropertyType.string, optional: true),
      SchemaProperty('pCountryId', RealmPropertyType.int, optional: true),
      SchemaProperty('pCountry', RealmPropertyType.string, optional: true),
      SchemaProperty('pRegion', RealmPropertyType.string, optional: true),
      SchemaProperty('pRegionId', RealmPropertyType.int, optional: true),
      SchemaProperty('pDeliveryComment', RealmPropertyType.string,
          optional: true),
      SchemaProperty('mobile', RealmPropertyType.string, optional: true),
      SchemaProperty('profileImageUrl', RealmPropertyType.string,
          optional: true),
      SchemaProperty('modifiedTimeStampProfile', RealmPropertyType.string,
          optional: true),
      SchemaProperty('adminAccess', RealmPropertyType.bool, optional: true),
      SchemaProperty('createTask', RealmPropertyType.bool, optional: true),
      SchemaProperty('orgIDs', RealmPropertyType.string, optional: true),
    ]);
  }();

  @override
  SchemaObject get objectSchema => RealmObjectBase.getSchema(this) ?? schema;
}
