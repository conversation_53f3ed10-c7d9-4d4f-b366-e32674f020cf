import 'package:flutter/material.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/network/network_info.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/cubits/connectivity_cubit.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_profile_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_misc_setting_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_history_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_alerts_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_availability_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_leave_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_skills_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_induction_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/update_pos_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/pos_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_user_state_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/user_state_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/address_skill_validation_entity.dart';
import 'package:storetrack_app/features/home/<USER>/models/address_skill_validation_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/address_skill_validation_mapper.dart';
import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/core/services/msal_auth_service.dart';
import 'package:storetrack_app/core/services/docs_interaction_service.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/send_opened_docs_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/entities/opened_doc_entity.dart';

class SyncService {
  final NetworkInfo _networkInfo;

  SyncService._internal() : _networkInfo = sl<NetworkInfo>();

  static final SyncService _instance = SyncService._internal();

  factory SyncService() => _instance;

  final ValueNotifier<bool> isSyncing = ValueNotifier(false);

  // User state validation notifications
  final ValueNotifier<Map<String, dynamic>?> userStateValidationResult =
      ValueNotifier(null);

  /// @deprecated This method is no longer needed as sync() manages its own indicator
  /// Start submission process and immediately show sync indicator
  @Deprecated(
      'Use sync() method directly - it manages the sync indicator internally')
  void startSubmission() {
    logger('🚀 Starting task submission - sync indicator activated');
    isSyncing.value = true;
  }

  Future<void> sync({
    String? userId,
    BuildContext? context,
    bool force = false,
    bool isEmulate = false,
  }) async {
    if (isSyncing.value && !force) {
      logger('⏸️ Sync already in progress, skipping');
      return;
    }

    // Check if work offline mode is enabled
    final connectivityCubit = sl<ConnectivityCubit>();
    if (connectivityCubit.isWorkOfflineEnabled) {
      logger('⏸️ Work offline mode is enabled, sync disabled');
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sync is disabled while working offline'),
            duration: Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // Check internet connectivity before starting sync
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected && context != null && context.mounted) {
      logger('❌ No internet connection available for sync');
      await _showNoInternetDialog(context);
      return;
    } else if (!isConnected) {
      logger(
          '❌ No internet connection available for sync (no context provided)');
      throw Exception('No internet connection available for sync');
    }

    final kUserId = userId ?? await sl<DataManager>().getUserId() ?? '';
    final syncStartTime = DateTime.now();

    logger('🚀 Starting full sync process for user: $kUserId');

    try {
      isSyncing.value = true;

      // Sequential photo and signature synchronization
      logger('📸 Phase 1: Starting photo and signature sync');
      await _syncPhotosAndSignatures(emulatedUserId: userId);
      logger('✅ Phase 1: Photo and signature sync completed');

      // Final step: Submit reports for completed tasks
      logger('📋 Phase 2: Starting report submission');
      await _submitReports(emulatedUserId: userId);
      logger('✅ Phase 2: Report submission completed');

      // Run basic sync operations in parallel
      logger('🔄 Phase 3: Starting parallel data sync operations');
      await Future.wait([
        _getTasks(emulatedUserId: userId),
        _syncCalendar(emulatedUserId: userId),
        _syncProfile(emulatedUserId: userId),
        _syncMiscSetting(emulatedUserId: userId),
        // Add new sync methods
        _syncHistory(emulatedUserId: userId),
        _syncAlerts(emulatedUserId: userId),
        _syncAvailability(emulatedUserId: userId),
        _syncLeave(emulatedUserId: userId),
        _syncSkills(emulatedUserId: userId),
        _syncInduction(emulatedUserId: userId),
        _syncOpenedDocs(emulatedUserId: userId),
      ]);
      logger('✅ Phase 3: Parallel data sync operations completed');

      // Final step: Sync user state and process validation data
      logger('👤 Phase 4: Starting user state sync');
      if (!isEmulate) await _syncUserState(emulatedUserId: userId);
      logger('✅ Phase 4: User state sync completed');

      // Perform user state validation after sync is complete
      logger('🔍 Phase 5: Starting user state validation');
      if (!isEmulate) await _performUserStateValidation(emulatedUserId: userId);
      logger('✅ Phase 5: User state validation completed');

      // Save sync completion time after successful sync
      if (!isEmulate) await sl<DataManager>().saveLastSyncTime(DateTime.now());

      final syncDuration = DateTime.now().difference(syncStartTime);
      logger(
          '🎉 Full sync completed successfully for user: $kUserId in ${syncDuration.inSeconds}s');
    } catch (e) {
      final syncDuration = DateTime.now().difference(syncStartTime);
      logger(
          '❌ Full sync failed for user: $kUserId after ${syncDuration.inSeconds}s - Error: $e');
      rethrow;
    } finally {
      isSyncing.value = false;
      logger('🏁 Sync process finalized, sync indicator deactivated');
    }
  }

  /// Sync method specifically for emulation - only calls 3 essential APIs
  /// 1. tasks_optimize (getTasks)
  /// 2. history
  /// 3. update_pos
  Future<void> syncForEmulation({required String emulatedUserId}) async {
    sync(userId: emulatedUserId);
    // if (isSyncing.value) return;

    // try {
    //   isSyncing.value = true;
    //   logger('🔄 Starting emulation sync for user: $emulatedUserId');

    //   // Run only the 3 essential APIs for emulation
    //   await Future.wait([
    //     _getTasks(emulatedUserId: emulatedUserId),
    //     _syncHistory(emulatedUserId: emulatedUserId),
    //     _syncUpdatePos(emulatedUserId: emulatedUserId),
    //   ]);

    //   logger('✅ Emulation sync completed successfully');

    //   // Save sync completion time after successful sync
    //   await sl<DataManager>().saveLastSyncTime(DateTime.now());
    // } finally {
    //   isSyncing.value = false;
    // }
  }

  Future<void> _getTasks({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('📋 Starting tasks sync for user: $userId');

      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      const String actualAppVersion = AppConstants.appVersion;

      final request = TasksRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        appversion: actualAppVersion,
        tasks: const [],
        token: token,
      );

      logger('📡 Calling tasks_optimize API for user: $userId');
      await sl<GetTasksUseCase>().call(request, isSync: true);
      logger('✅ Tasks sync completed successfully for user: $userId');

      // The repository now handles caching, so no need to save here.
      // if (result.isSuccess && result.data != null) {
      //   await sl<HomeLocalDataSource>().saveTasks(result.data!);
      // }
    } catch (e) {
      logger('❌ Tasks sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncCalendar({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('📅 Starting calendar sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      final params = GetCalendarParams(token: token, userId: id);
      await sl<GetCalendarUseCase>().call(params, isSync: true);

      logger('✅ Calendar sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Calendar sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncProfile({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('👤 Starting profile sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetProfileUseCase>()
          .call(token: token, userId: id, isSync: true);

      logger('✅ Profile sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Profile sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  Future<void> _syncMiscSetting({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('⚙️ Starting misc settings sync for user: $userId');

      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetMiscSettingUseCase>().call(token: token, userId: id);

      logger('✅ Misc settings sync completed successfully for user: $userId');
    } catch (e) {
      logger('❌ Misc settings sync failed for user: $userId - Error: $e');
      rethrow;
    }
  }

  /// Sequential photo and signature synchronization
  ///
  /// This method performs the following operations in sequence:
  /// 1. Upload photos API endpoint
  /// 2. Sync photos API endpoint (after successful photo upload)
  /// 3. Upload signatures API endpoint
  /// 4. Sync signatures API endpoint (after successful signature upload)
  Future<void> _syncPhotosAndSignatures({String? emulatedUserId}) async {
    try {
      // Get all tasks from local database
      final tasks = await _getAllTasksFromDatabase();

      if (tasks.isEmpty) {
        logger('No tasks found for photo and signature sync');
        return;
      }

      // Step 1 & 2: Upload and sync photos
      logger('🚀 Starting photo upload and sync workflow');
      final photoSyncResult = await sl<HomeRepository>().uploadAndSyncPhotos(
        tasks: tasks,
      );

      if (photoSyncResult.isSuccess) {
        logger('✅ Photo upload and sync completed successfully');
      } else {
        logger('❌ Photo upload and sync failed: ${photoSyncResult.error}');
      }

      // Step 3 & 4: Upload and sync signatures
      logger('🚀 Starting signature upload and sync workflow');
      final signatureSyncResult =
          await sl<HomeRepository>().uploadAndSyncSignatures(
        tasks: tasks,
      );

      if (signatureSyncResult.isSuccess) {
        logger('✅ Signature upload and sync completed successfully');
      } else {
        logger(
            '❌ Signature upload and sync failed: ${signatureSyncResult.error}');
      }
    } catch (e) {
      logger('❌ Error during photo and signature sync: $e');
    }
  }

  /// Get all tasks from the local database
  Future<List<TaskDetail>> _getAllTasksFromDatabase() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskModels = realm.all<TaskDetailModel>();

      // Convert models to entities
      final tasks =
          taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

      logger('Retrieved ${tasks.length} tasks from local database');
      return tasks;
    } catch (e) {
      logger('❌ Error retrieving tasks from database: $e');
      return [];
    }
  }

  /// Submit reports for tasks that need to be submitted
  ///
  /// This method performs the final step in the sync process by submitting
  /// completed task reports to the server using the new submit report batch API.
  Future<void> _submitReports({String? emulatedUserId}) async {
    try {
      // Filter tasks that need to be submitted (have sync pending status)
      final tasksToSubmit = SyncUtils.getTasksToSubmit();

      if (tasksToSubmit.isEmpty) {
        logger('No tasks require report submission');
        return;
      }

      logger('🚀 Starting report submission for ${tasksToSubmit.length} tasks');

      // Submit each task individually
      for (final task in tasksToSubmit) {
        try {
          // Create submit report request for this task
          final submitRequest = await SyncUtils.createSubmitReportRequest(
            task: task,
          );

          // Submit the report using the new batch API
          final submitResult = await sl<HomeRepository>().submitReportBatch(
            submitRequest,
          );

          if (submitResult.isSuccess) {
            logger('✅ Report submitted successfully for task ${task.taskId}');

            // Clear sync pending status for this task
            await _clearTaskSyncPending(task.taskId?.toString() ?? '');
          } else {
            logger(
                '❌ Report submission failed for task ${task.taskId}: ${submitResult.error}');
          }
        } catch (e) {
          logger('❌ Error submitting report for task ${task.taskId}: $e');
        }
      }

      logger('✅ Report submission process completed');
    } catch (e) {
      logger('❌ Error during report submission: $e');
    }
  }

  /// Clear sync pending status for a task
  Future<void> _clearTaskSyncPending(String taskId) async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskIdInt = int.tryParse(taskId);

      if (taskIdInt == null) {
        logger('Invalid taskId format: $taskId');
        return;
      }

      final task = realm.query<TaskDetailModel>(
        'taskId == \$0',
        [taskIdInt],
      ).firstOrNull;

      if (task == null) {
        logger('Task not found with ID: $taskId');
        return;
      }

      realm.write(() {
        task.syncPending = false;
        task.isSynced = true;
      });

      logger('Successfully cleared sync pending status for task $taskId');
    } catch (e) {
      logger('Error clearing sync pending status for task $taskId: $e');
    }
  }

  // Add new sync methods for emulation support
  Future<void> _syncHistory({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetHistoryUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing history: $e');
    }
  }

  Future<void> _syncAlerts({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAlertsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing alerts: $e');
    }
  }

  Future<void> _syncAvailability({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetAvailabilityUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing availability: $e');
    }
  }

  Future<void> _syncLeave({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetLeaveUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing leave: $e');
    }
  }

  Future<void> _syncSkills({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetSkillsUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing skills: $e');
    }
  }

  Future<void> _syncInduction({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';

      await sl<GetInductionUseCase>().call(
        token: token,
        userId: id, // Use emulated user's ID if provided
        isSync: true,
      );
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing induction: $e');
    }
  }

  Future<void> _syncUpdatePos({String? emulatedUserId}) async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = emulatedUserId ?? await sl<DataManager>().getUserId() ?? '';
      final String deviceUid = await sl<DataManager>().getOrCreateDeviceId();

      // Create a basic request to sync POS data for the emulated user
      final updatePosRequest = UpdatePosRequestEntity(
        token: token,
        userId: int.tryParse(id),
        pos: [], // Empty array for sync request
        deviceuid: deviceUid,
      );

      await sl<UpdatePosUseCase>().call(updatePosRequest);
      logger('✅ POS sync completed for user: $id');
    } catch (e) {
      // Handle error appropriately
      logger('Error syncing update pos: $e');
    }
  }

  Future<void> _syncUserState({String? emulatedUserId}) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      const String actualAppVersion = AppConstants.appVersion;

      final request = UserStateRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        devicePlatform: 'android', // or 'ios' based on platform
        appversion: actualAppVersion,
        token: token,
      );

      logger('🚀 Starting user state sync');

      final result = await sl<GetUserStateUseCase>().call(request: request);

      if (result.isSuccess && result.data?.data != null) {
        final userStateData = result.data!.data!;

        // Save preferences
        await dataManager.saveAdminAccess(userStateData.adminAccess);
        await dataManager.saveCreateTask(userStateData.createTask);
        await dataManager.saveSupportNumber(userStateData.supportNumber);

        logger(
            '✅ User state preferences saved: adminAccess=${userStateData.adminAccess}, createTask=${userStateData.createTask}, supportNumber=${userStateData.supportNumber}');

        // Process address/skill validation
        await _processAddressSkillValidationDataFromServer(
            userStateData.warningTitle);

        logger('✅ User state sync completed successfully');
      } else {
        logger('❌ User state sync failed: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error during user state sync: $e');
    }
  }

  Future<void> _processAddressSkillValidationDataFromServer(
      String warningTitle) async {
    try {
      final realm = sl<RealmDatabase>().realm;

      // Create entity from warning title
      final validationEntity =
          AddressSkillValidationEntity.fromWarningTitle(warningTitle);

      realm.write(() {
        // Delete existing validation records
        final existingModels = realm.all<AddressSkillValidationModel>();
        realm.deleteMany(existingModels);

        // Add new validation model
        final newModel = AddressSkillValidationMapper.toModel(validationEntity);
        realm.add(newModel);
      });

      logger(
          '✅ Address/Skill validation data processed for warningTitle: $warningTitle');
    } catch (e) {
      logger('❌ Error processing address/skill validation data: $e');
    }
  }

  /// Perform user state validation after sync completion
  Future<void> _performUserStateValidation({String? emulatedUserId}) async {
    try {
      final dataManager = sl<DataManager>();
      final token = await dataManager.getAuthToken() ?? '';
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
      const String actualAppVersion = AppConstants.appVersion;

      final request = UserStateRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        devicePlatform: 'android', // or 'ios' based on platform
        appversion: actualAppVersion,
        token: token,
      );

      logger('🚀 Starting user state validation');

      final result = await sl<GetUserStateUseCase>().call(request: request);

      if (result.isSuccess && result.data?.data != null) {
        final userStateData = result.data!.data!;

        logger(
            '📊 User state data: active=${userStateData.active}, appOldVersion=${userStateData.appOldVersion}, gracePeriod=${userStateData.gracePeriod}, kickOutUser=${userStateData.kickOutUser}, appOtherWarning=${userStateData.appOtherWarning}');

        // Check if user is active
        if (!userStateData.active && (emulatedUserId == null)) {
          logger('❌ User is inactive - kicking out');
          await _kickOutUser();
          return;
        }

        // Check for address/skills validation issues first (higher priority)
        if (userStateData.appOtherWarning) {
          final loginResponse = await dataManager.getLoginResponse();
          final isAutoScheduleEnabled =
              loginResponse?.data?.premAutoSchedule ?? false;

          if (isAutoScheduleEnabled) {
            logger(
                '⚠️ Address/Skills validation required - showing custom message and redirecting');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              userStateData.kickOutUser,
            );
            return;
          }
        }

        // Check for app version issues
        if (userStateData.appOldVersion && !userStateData.gracePeriod) {
          if (userStateData.kickOutUser) {
            logger(
                '❌ App version outdated and grace period expired - kicking out');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              true,
            );
          } else {
            logger(
                '⚠️ App version outdated but user not being kicked out - showing warning');
            await _kickOutUserWithCustomMessage(
              userStateData.warningMessage,
              userStateData.warningTitle,
              false,
            );
          }
        }

        logger('✅ User state validation completed successfully');
      } else {
        logger('❌ User state validation failed: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error during user state validation: $e');
    }
  }

  /// Kick out inactive user (user is inactive)
  Future<void> _kickOutUser() async {
    try {
      logger('🚪 Kicking out inactive user');

      // Clear all user data
      await sl<DataManager>().clearAll();
      sl<RealmDatabase>().clearAllData();

      // Notify UI layer to show kick out dialog and navigate to login
      userStateValidationResult.value = {
        'action': 'kickOut',
        'title': 'Invalid User Session',
        'message':
            'You have been logged out as we have detected multiple user session on a similar device.',
        'navigateToLogin': true,
      };

      logger('✅ User kicked out successfully');
    } catch (e) {
      logger('❌ Error during user kick out: $e');
    }
  }

  /// Kick out user with custom message (app version or address/skills issues)
  Future<void> _kickOutUserWithCustomMessage(
      String warningMessage, String warningTitle, bool kickOut) async {
    try {
      logger('🚪 Kicking out user with custom message: $warningTitle');

      if (kickOut) {
        // Clear user data if kicking out
        await sl<DataManager>().clearAll();
        sl<RealmDatabase>().clearAllData();

        // Sign out MSAL if applicable
        final dataManager = sl<DataManager>();
        final userEmail = await dataManager.getEmail();
        final isMsalUser = userEmail?.contains('@dkshsmollan.com') == true ||
            userEmail?.contains('@dksh.com') == true;

        if (isMsalUser) {
          final msalAuthService = sl<MsalAuthService>();
          if (msalAuthService.isInitialized) {
            await msalAuthService.signOut();
          }
        }
      }

      // Notify UI layer to show custom message dialog
      userStateValidationResult.value = {
        'action': 'customMessage',
        'title': warningTitle,
        'message': warningMessage,
        'kickOut': kickOut,
        'navigateToLogin': kickOut,
        'checkAddressSkills': !kickOut,
      };

      // If not kicking out, check address/skills validation after user dismisses dialog
      if (!kickOut) {
        await _checkAddressSkillsValidation();
      }

      logger('✅ User kick out with custom message completed');
    } catch (e) {
      logger('❌ Error during user kick out with custom message: $e');
    }
  }

  /// Redirect to profile for address/skills validation
  Future<void> _redirectToProfile() async {
    try {
      logger('🔄 Redirecting to profile for address/skills validation');

      // Notify UI layer to navigate to profile
      userStateValidationResult.value = {
        'action': 'redirectToProfile',
        'navigateToProfile': true,
      };

      logger('✅ Redirect to profile completed');
    } catch (e) {
      logger('❌ Error during profile redirect: $e');
    }
  }

  Future<void> _syncOpenedDocs({String? emulatedUserId}) async {
    final userId =
        emulatedUserId ?? await sl<DataManager>().getUserId() ?? 'unknown';

    try {
      logger('📄 Starting opened docs sync for user: $userId');

      final docsService = sl<DocsInteractionService>();
      final openedDocs = await docsService.getOpenedDocs();

      if (openedDocs.isEmpty) {
        logger('No opened docs to sync for user: $userId');
        return;
      }

      logger(
          'Found ${openedDocs.length} opened docs to sync for user: $userId');

      final dataManager = sl<DataManager>();
      final id = emulatedUserId ?? await dataManager.getUserId() ?? '';
      final String token = await dataManager.getFcmToken() ?? '';

      final request = OpenedDocRequestEntity(
        userId: id,
        deviceToken: token,
        messageRead: openedDocs,
      );

      final result = await sl<SendOpenedDocsUseCase>().call(request);

      if (result.isSuccess) {
        logger('✅ Opened docs sync successful for user: $userId');

        // Clear local interactions after successful sync
        final clearResult = await docsService.clearOpenedDocs();
        if (clearResult) {
          logger('✅ Local opened docs cleared successfully for user: $userId');
        } else {
          logger('⚠️ Failed to clear local opened docs for user: $userId');
        }
      } else {
        logger(
            '❌ Opened docs sync failed for user: $userId - Error: ${result.error}');
      }
    } catch (e) {
      logger('❌ Error syncing opened docs for user: $userId - Error: $e');
    }
  }

  /// Check if address/skills validation is needed
  Future<void> _checkAddressSkillsValidation() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final validationModel =
          realm.all<AddressSkillValidationModel>().firstOrNull;

      if (validationModel == null) {
        logger('⚠️ No address/skills validation data found');
        return;
      }

      final validationEntity =
          AddressSkillValidationMapper.toEntity(validationModel);

      // Check if any validation is missing
      final needsValidation = !validationEntity.isAddressValidated ||
          !validationEntity.isSkillValidated ||
          !validationEntity.isAvailabilityValidated;

      if (needsValidation) {
        final dataManager = sl<DataManager>();
        final loginResponse = await dataManager.getLoginResponse();
        final isAutoScheduleEnabled =
            loginResponse?.data?.premAutoSchedule ?? false;

        if (isAutoScheduleEnabled) {
          logger(
              '🔄 Address/Skills validation needed - redirecting to profile');
          await _redirectToProfile();
        }
      } else {
        logger('✅ Address/Skills validation complete');
      }
    } catch (e) {
      logger('❌ Error during address/skills validation check: $e');
    }
  }

  /// Show no internet connection dialog with single OK button
  Future<void> _showNoInternetDialog(BuildContext context) async {
    if (!context.mounted) return;

    await ConfirmDialog.show(
      context: context,
      title: 'No Internet Connection',
      message:
          'Sync requires an internet connection. Please check your connection and try again.',
      confirmText: 'OK',
      onConfirm: () {
        // User acknowledged, do nothing
        logger('User acknowledged no internet connection');
      },
    );
  }
}
