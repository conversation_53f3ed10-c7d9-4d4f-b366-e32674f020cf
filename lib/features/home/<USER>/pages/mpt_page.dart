import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/services/camera_service.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/shared/widgets/offline_image_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/core/services/photo_service.dart';

// View modes enum
enum ViewMode { stack, list, edit }

// Photo section class to manage sections with images
class PhotoSection {
  final String sectionId;
  final String sectionTitle;
  final List<PhotoItem> images;
  final List<TextEditingController> textControllers;
  final int? photoTagId; // Add photoTagId for better section matching
  final int? measurementPhototypeId; // Add measurementPhototypeId
  final entities.PhotoTagsT? photoTag; // Store the full photo tag object

  PhotoSection({
    required this.sectionId,
    required this.sectionTitle,
    required this.images,
    required this.textControllers,
    this.photoTagId,
    this.measurementPhototypeId,
    this.photoTag,
  });

  void dispose() {
    for (var controller in textControllers) {
      controller.dispose();
    }
  }
}

// Photo item class
class PhotoItem {
  final String id;
  final String url;
  String description;
  final int? photoId; // Database photo ID
  final bool isFromDatabase; // Track if photo is from database
  final bool isCannotUpload; // Track if this is a cannot upload photo

  PhotoItem({
    required this.id,
    required this.url,
    required this.description,
    this.photoId,
    this.isFromDatabase = false,
    this.isCannotUpload = false,
  });
}

@RoutePage()
class MPTPage extends StatefulWidget {
  final List<String>? images;
  final String? taskId;
  final String? formId;
  final String? questionId;
  final String? questionPartId;
  final String? measurementId;
  final String? combineTypeId;
  final String? questionPartMultiId;
  final entities.Question? question;
  final int level;
  final int? photoTagId;

  const MPTPage({
    super.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
    this.images,
    this.question,
    this.level = 2,
    this.photoTagId,
  });

  @override
  State<MPTPage> createState() => _MPTPageState();
}

class _MPTPageState extends State<MPTPage> {
  ViewMode _currentMode = ViewMode.stack;
  late final CameraService _cameraService;
  late final PhotoService _photoService;
  late List<PhotoSection> _sections;
  final Set<String> _selectedImageIds = <String>{};

  @override
  void initState() {
    super.initState();
    _cameraService = sl<CameraService>();
    _photoService = sl<PhotoService>();
    _initializePage();
  }

  Future<void> _initializePage() async {
    _initializeSections();
    await _loadSavedPhotos();
  }

  void _initializeSections() {
    _sections = [];

    List<entities.PhotoTagsT>? photoTags;
    if (widget.level == 2) {
      photoTags = widget.question?.photoTagsTwo;
    } else if (widget.level == 3) {
      photoTags = widget.question?.photoTagsThree;
    }

    photoTags = photoTags
        ?.where((photoTag) => photoTag.photoTagId == widget.photoTagId)
        .toList();

    if (photoTags != null && photoTags.isNotEmpty) {
      List<entities.PhotoTagsT> filteredPhotoTags = photoTags;

      if (widget.questionPartId != null && widget.measurementId != null) {
        final questionPartIdNum = num.tryParse(widget.questionPartId!);
        final measurementIdNum = num.tryParse(widget.measurementId!);

        filteredPhotoTags = photoTags.where((photoTag) {
          return photoTag.questionpartId == questionPartIdNum &&
              photoTag.measurementId == measurementIdNum &&
              photoTag.photoTagId == widget.photoTagId;
        }).toList();
      }

      logger('Filtered photo tags: ${filteredPhotoTags.length}');
      for (int i = 0; i < filteredPhotoTags.length; i++) {
        final photoTag = filteredPhotoTags[i];
        final sectionTitle = photoTag.photoTag ?? 'Add Image';
        final sectionId = 'section_$i';

        _sections.add(PhotoSection(
          sectionId: sectionId,
          sectionTitle: sectionTitle,
          images: [],
          textControllers: [],
          photoTagId: photoTag.photoTagId?.toInt(),
          measurementPhototypeId: photoTag.measurementPhototypeId?.toInt(),
          photoTag: photoTag,
        ));
      }
    } else {
      _sections.add(PhotoSection(
        sectionId: 'default_section',
        sectionTitle: 'Add Image',
        images: [],
        textControllers: [],
      ));
    }
  }

  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || widget.questionId == null) {
      logger('TaskId or QuestionId is null, cannot load saved photos');
      return;
    }

    try {
      final taskId = int.tryParse(widget.taskId!);
      final questionId = int.tryParse(widget.questionId!);
      final measurementId = widget.measurementId != null
          ? int.tryParse(widget.measurementId!)
          : null;
      final questionPartId = widget.questionPartId != null
          ? int.tryParse(widget.questionPartId!)
          : null;

      if (taskId == null || questionId == null) {
        logger('Invalid taskId or questionId format');
        return;
      }

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      final filteredPhotos = savedPhotos.where((photo) {
        final questionIdMatch = photo.questionId?.toInt() == questionId;
        bool measurementIdMatch = measurementId == null ||
            photo.measurementId?.toInt() == measurementId;
        bool questionPartIdMatch = questionPartId == null ||
            photo.questionpartId?.toInt() == questionPartId;
        final levelMatch = photo.combineTypeId?.toInt() == widget.level;

        return questionIdMatch &&
            measurementIdMatch &&
            questionPartIdMatch &&
            levelMatch &&
            (photo.photoTagId == widget.photoTagId ||
                photo.folderId == widget.photoTagId);
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          for (final photo in filteredPhotos) {
            int sectionIndex = _findSectionForPhoto(photo);
            if (sectionIndex != -1) {
              final section = _sections[sectionIndex];

              // Handle cannot upload photos
              if (photo.cannotUploadMandatory == true) {
                final photoItem = PhotoItem(
                  id: 'db_cannot_upload_${photo.photoId}',
                  url:
                      'cannot_upload', // Placeholder URL for cannot upload photos
                  description: photo.caption ?? 'Cannot upload',
                  photoId: photo.photoId?.toInt(),
                  isFromDatabase: true,
                  isCannotUpload: true,
                );

                section.images.add(photoItem);
                section.textControllers
                    .add(TextEditingController(text: photoItem.description));
              } else {
                // Handle regular photos (with actual image files)
                final imageUrl = photo.photoUrl ?? photo.localPath;
                if (imageUrl != null) {
                  final photoItem = PhotoItem(
                    id: 'db_photo_${photo.photoId}',
                    url: imageUrl,
                    description: photo.caption ?? 'Saved photo',
                    photoId: photo.photoId?.toInt(),
                    isFromDatabase: true,
                    isCannotUpload: false,
                  );

                  section.images.add(photoItem);
                  section.textControllers
                      .add(TextEditingController(text: photoItem.description));
                }
              }
            }
          }
        });
      }
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  int _findSectionForPhoto(entities.Photo photo) {
    if (photo.photoTagId != null) {
      final index =
          _sections.indexWhere((s) => s.photoTagId == photo.folderId?.toInt());
      if (index != -1) return index;
    }
    final index = _sections
        .indexWhere((s) => s.photoTag?.photoTagId == photo.folderId?.toInt());
    for (final s in _sections) {
      logger(
          "aaa ${s.photoTagId.toString()} ${photo.folderId.toString()} ${_sections.indexOf(s)}");
    }
    if (index != -1) return index;

    return _sections.isNotEmpty ? 0 : -1;
  }

  @override
  void dispose() {
    for (var section in _sections) {
      section.dispose();
    }
    super.dispose();
  }

  int get _totalImages {
    return _sections.fold(0, (total, section) => total + section.images.length);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: 'Photos',
        actions: [
          _buildAppBarButton(
            icon: Icons.grid_view,
            isSelected: _currentMode == ViewMode.stack,
            onTap: () => _setViewMode(ViewMode.stack),
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.view_list,
            isSelected: _currentMode == ViewMode.list,
            onTap: () => _setViewMode(ViewMode.list),
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.camera_alt,
            isSelected: false,
            onTap: () => _onCameraTap(0),
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.edit,
            isSelected: _currentMode == ViewMode.edit,
            onTap: () => _setViewMode(ViewMode.edit),
          ),
          const Gap(16),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: _currentMode == ViewMode.edit
          ? Container(
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.midGrey,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _setViewMode(ViewMode.stack);
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.close_rounded,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                  const Gap(8),
                  SizedBox(
                    width: 96,
                    child: AppButton(
                      text: _selectedImageIds.length == _totalImages
                          ? "Deselect all"
                          : "Select all",
                      color: Colors.white,
                      textColor: AppColors.black,
                      onPressed: _toggleSelectAll,
                      height: 40,
                    ),
                  ),
                  const Gap(8),
                  if (_selectedImageIds.isNotEmpty)
                    SizedBox(
                      width: 64,
                      child: AppButton(
                        text: "Delete",
                        color: Colors.white,
                        textColor: AppColors.black,
                        onPressed: _selectedImageIds.isNotEmpty
                            ? _onDeleteSelectedImages
                            : () {},
                        height: 40,
                      ),
                    ),
                  if (_selectedImageIds.isNotEmpty) const Gap(8),
                  SizedBox(
                    width: 64,
                    child: AppButton(
                      text: "Save",
                      color: AppColors.primaryBlue,
                      onPressed: () {
                        // Handle save action
                        _setViewMode(ViewMode.stack);
                      },
                      height: 40,
                    ),
                  ),
                ],
              ),
            )
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildAppBarButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
        child: Icon(
          icon,
          size: 18,
          color: isSelected ? AppColors.primaryBlue : AppColors.blackTint1,
        ),
      ),
    );
  }

  void _setViewMode(ViewMode mode) {
    setState(() {
      _currentMode = mode;
      if (mode != ViewMode.edit) {
        _selectedImageIds.clear();
      }
    });
  }

  void _onCameraTap(int sectionIndex) {
    if (_sections.isEmpty) {
      SnackBarService.error(
          context: context, message: "No sections available to add photos.");
      return;
    }
    // If there's only a default section, use it. Otherwise, let user choose.
    if (_sections.length == 1) {
      _showImageSourceDialog(0);
    } else {
      _showImageSourceDialog(
          sectionIndex); // This part might need a section selector UI if multiple sections exist
    }
  }

  void _showImageSourceDialog(int sectionIndex) {
    // Get photoTag settings for this section
    final section = _sections[sectionIndex];
    final photoTag = section.photoTag;
    // final liveImagesOnly = photoTag?.liveImagesOnly == true;

    // If only live images are allowed, directly open camera
    // if (liveImagesOnly) {
    //   _captureImage(ImageSource.camera, sectionIndex);
    //   return;
    // }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      useRootNavigator: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(16),
                Text('Add photo',
                    style: Theme.of(context)
                        .textTheme
                        .montserratNavigationPrimaryMedium),
                const Gap(24),
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.camera_alt,
                        color: AppColors.primaryBlue, size: 20),
                  ),
                  title: Text('Take Photo',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'Use camera to capture a new photo',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    context.router.maybePop();
                    _captureImage(ImageSource.camera, sectionIndex);
                  },
                ),
                // Only show gallery option if live images only is false
                if (photoTag?.liveImagesOnly != true)
                  ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.photo_library,
                          color: Colors.green, size: 20),
                    ),
                    title: Text('Choose from Gallery',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall),
                    subtitle: Text(
                      'Select an existing photo from gallery',
                      style: Theme.of(context).textTheme.montserratTableSmall,
                    ),
                    onTap: () {
                      context.router.maybePop();
                      _captureImage(ImageSource.gallery, sectionIndex);
                    },
                  ),
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.image_not_supported_rounded,
                        color: Colors.orange, size: 20),
                  ),
                  title: Text('Cannot Upload',
                      style: Theme.of(context)
                          .textTheme
                          .montserratTitleExtraSmall),
                  subtitle: Text(
                    'I cannot upload this photo',
                    style: Theme.of(context).textTheme.montserratTableSmall,
                  ),
                  onTap: () {
                    context.router.maybePop();
                    _handleCannotUpload(sectionIndex);
                  },
                ),
                const Gap(16),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _captureImage(ImageSource source, int sectionIndex) async {
    try {
      // Get the photo tag for this section to determine compression and source settings
      final section = _sections[sectionIndex];
      final photoTag = section.photoTag;

      // Determine image quality based on photoTag settings
      int? imageQuality;
      if (photoTag?.imageRec == true) {
        // Disable compression for high-resolution images
        imageQuality = null;
      } else {
        // Use PhotoResPerc as compression percentage, default to 85 if not specified
        imageQuality = photoTag?.photoResPerc?.toInt() ?? 85;
      }

      // Check if only live images (camera) are allowed
      final liveImagesOnly = photoTag?.liveImagesOnly == true;

      final File? imageFile = await _cameraService.pickImage(
        source: source,
        imageQuality: imageQuality,
        liveImagesOnly: liveImagesOnly,
      );
      if (imageFile == null) return;

      if (sectionIndex >= _sections.length) {
        if (mounted) {
          SnackBarService.error(
              context: context, message: 'Invalid section index.');
        }
        return;
      }

      await _saveImageWithCaption(imageFile, 'Photo', sectionIndex);
      // Show fullscreen image viewer
      // if (mounted) {
      //   showDialog(
      //     context: context,
      //     barrierColor: Colors.black,
      //     builder: (context) => FullscreenImageViewer(
      //       imagePath: imageFile.path,
      //       initialCaption: 'Photo',
      //       onRetake: () {
      //         // Retake photo - trigger the picker again
      //         _captureImage(source, sectionIndex);
      //       },
      //       onUsePhoto: (caption) async {
      //         // Save the photo with the entered caption
      //         await _saveImageWithCaption(imageFile, caption, sectionIndex);
      //       },
      //     ),
      //   );
      // }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
            context: context,
            message: 'Failed to capture photo: ${e.toString()}');
      }
    }
  }

  Future<void> _saveImageWithCaption(
      File imageFile, String caption, int sectionIndex) async {
    try {
      final String? savedImagePath =
          await ImageStorageUtils.saveImageToAppStorage(imageFile);
      if (savedImagePath == null) {
        if (mounted) {
          SnackBarService.error(
              context: context, message: 'Failed to save photo.');
        }
        return;
      }

      // Remove any existing cannot upload photos from this section before saving the actual photo
      await _removeExistingCannotUploadPhotos(sectionIndex);

      final section = _sections[sectionIndex];
      final photoSaved =
          await _savePhotoToDatabase(savedImagePath, section.photoTag, caption);

      if (photoSaved != null) {
        setState(() {
          final newImageId =
              '${section.sectionId}_image_${section.images.length}';
          final newPhotoItem = PhotoItem(
            id: newImageId,
            url: savedImagePath,
            description: caption,
            photoId: photoSaved.photoId?.toInt(),
            isFromDatabase: true,
          );
          section.images.add(newPhotoItem);
          section.textControllers
              .add(TextEditingController(text: newPhotoItem.description));
        });
        if (mounted) {
          SnackBarService.success(
              context: context, message: 'Photo added successfully!');
        }
      } else {
        if (mounted) {
          SnackBarService.error(
              context: context, message: 'Failed to save photo to database.');
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
            context: context, message: 'Failed to save photo: ${e.toString()}');
      }
    }
  }

  Future<entities.Photo?> _savePhotoToDatabase(
      String photoPath, entities.PhotoTagsT? photoTag,
      [String? caption]) async {
    try {
      if (widget.taskId == null || widget.questionId == null) {
        logger('TaskId or QuestionId is null, cannot save photo');
        return null;
      }

      final taskId = int.tryParse(widget.taskId!);
      final questionId = int.tryParse(widget.questionId!);
      final measurementId = widget.measurementId != null
          ? int.tryParse(widget.measurementId!)
          : null;
      final questionpartId = widget.questionPartId != null
          ? int.tryParse(widget.questionPartId!)
          : null;
      final measurementPhototypeId = widget.questionPartMultiId != null
          ? int.tryParse(widget.questionPartMultiId!)
          : null;

      if (taskId == null || questionId == null) {
        logger('Invalid taskId or questionId format');
        return null;
      }

      final photoId = await _photoService.getNextPhotoId(taskId: taskId);

      final photo = entities.Photo(
        photoId: photoId,
        formId: widget.formId != null ? int.tryParse(widget.formId!) : null,
        questionId: questionId,
        measurementId: measurementId,
        measurementPhototypeId:
            photoTag?.measurementPhototypeId?.toInt() ?? measurementPhototypeId,
        questionpartId: questionpartId,
        questionPartMultiId: widget.questionPartMultiId,
        combineTypeId: widget.level,
        caption: caption ?? 'Photo',
        // Use localPath for locally added images instead of photoUrl
        localPath: photoPath,
        modifiedTimeStampPhoto: DateTime.now(),
        userDeletedPhoto: false,
        cannotUploadMandatory: false,
        imageRec: false,
        photoTagId: photoTag?.photoTagId?.toInt(),
        photoCombinetypeId: widget.level,
        isEdited: false,
      );

      final success = await _photoService.savePhotoToTask(
        photo: photo,
        taskId: taskId,
        folderId: photoTag?.photoTagId?.toInt(),
      );

      return success ? photo : null;
    } catch (e) {
      logger('Error saving photo to database: $e');
      return null;
    }
  }

  /// Check if a cannot upload photo already exists in the specified section
  bool _hasExistingCannotUploadPhoto(int sectionIndex) {
    if (sectionIndex >= _sections.length) {
      return false;
    }

    final section = _sections[sectionIndex];
    
    // Search through the section's images for any cannot upload photos
    for (final image in section.images) {
      if (image.isCannotUpload) {
        logger('Found existing cannot upload photo in section $sectionIndex: ${image.id}');
        return true;
      }
    }
    
    logger('No existing cannot upload photo found in section $sectionIndex');
    return false;
  }

  /// Check if normal (non-cannot-upload) photos already exist in the specified section
  bool _hasExistingNormalPhotos(int sectionIndex) {
    if (sectionIndex >= _sections.length) {
      return false;
    }

    final section = _sections[sectionIndex];
    
    // Search through the section's images for any normal photos
    for (final image in section.images) {
      if (!image.isCannotUpload) {
        logger('Found existing normal photo in section $sectionIndex: ${image.id}');
        return true;
      }
    }
    
    logger('No existing normal photos found in section $sectionIndex');
    return false;
  }

  /// Remove existing cannot upload photos from the specified section
  Future<void> _removeExistingCannotUploadPhotos(int sectionIndex) async {
    if (sectionIndex >= _sections.length) {
      logger('Invalid section index: $sectionIndex');
      return;
    }

    if (widget.taskId == null) {
      logger('TaskId is null, cannot remove cannot upload photos');
      return;
    }

    final taskId = int.tryParse(widget.taskId!);
    if (taskId == null) {
      logger('Invalid taskId format');
      return;
    }

    final section = _sections[sectionIndex];
    final imagesToRemove = <int>[];
    final controllersToDispose = <TextEditingController>[];

    // Find cannot upload photos in this section
    for (int i = 0; i < section.images.length; i++) {
      final image = section.images[i];
      if (image.isCannotUpload && image.photoId != null) {
        logger('Removing cannot upload photo: photoId=${image.photoId}');
        
        // Delete from database
        final deleteSuccess = await _photoService.deletePhotoFromTask(
          photoId: image.photoId!,
          taskId: taskId,
          folderId: section.photoTagId,
        );
        
        if (deleteSuccess) {
          imagesToRemove.add(i);
          controllersToDispose.add(section.textControllers[i]);
        }
      }
    }

    // Remove from UI in reverse order to maintain indices
    if (imagesToRemove.isNotEmpty) {
      setState(() {
        for (final controller in controllersToDispose) {
          controller.dispose();
        }
        
        for (int i = imagesToRemove.length - 1; i >= 0; i--) {
          final indexToRemove = imagesToRemove[i];
          section.images.removeAt(indexToRemove);
          section.textControllers.removeAt(indexToRemove);
        }
      });
      logger('Removed ${imagesToRemove.length} cannot upload photos from section $sectionIndex');
    }
  }

  /// Handle cannot upload photo option
  Future<void> _handleCannotUpload(int sectionIndex) async {
    try {
      // Check if normal photos already exist in this section - if so, cannot add cannot upload
      if (_hasExistingNormalPhotos(sectionIndex)) {
        return;
      }
      
      // Check if a cannot upload photo already exists in this section
      if (_hasExistingCannotUploadPhoto(sectionIndex)) {
        return;
      }
      
      await _saveCannotUploadPhoto(sectionIndex);
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save cannot upload photo: ${e.toString()}',
        );
      }
    }
  }

  /// Save cannot upload photo to database
  Future<void> _saveCannotUploadPhoto(int sectionIndex) async {
    try {
      if (widget.taskId == null || widget.questionId == null) {
        logger('TaskId or QuestionId is null, cannot save cannot upload photo');
        return;
      }

      final taskId = int.tryParse(widget.taskId!);
      final questionId = int.tryParse(widget.questionId!);
      final measurementId = widget.measurementId != null
          ? int.tryParse(widget.measurementId!)
          : null;
      final questionpartId = widget.questionPartId != null
          ? int.tryParse(widget.questionPartId!)
          : null;
      final measurementPhototypeId = widget.questionPartMultiId != null
          ? int.tryParse(widget.questionPartMultiId!)
          : null;

      if (taskId == null || questionId == null) {
        logger('Invalid taskId or questionId format');
        return;
      }

      if (sectionIndex >= _sections.length) {
        logger('Invalid section index: $sectionIndex');
        return;
      }

      final section = _sections[sectionIndex];
      final photoTag = section.photoTag;
      final photoId = await _photoService.getNextPhotoId(taskId: taskId);

      final photo = entities.Photo(
        photoId: photoId,
        formId: widget.formId != null ? int.tryParse(widget.formId!) : null,
        questionId: questionId,
        measurementId: measurementId,
        measurementPhototypeId:
            photoTag?.measurementPhototypeId?.toInt() ?? measurementPhototypeId,
        questionpartId: questionpartId,
        questionPartMultiId: widget.questionPartMultiId,
        combineTypeId: widget.level,
        caption: 'Cannot upload',
        localPath: null, // No actual image file for cannot upload
        modifiedTimeStampPhoto: DateTime.now(),
        userDeletedPhoto: false,
        cannotUploadMandatory: true, // Set to true for cannot upload
        imageRec: false,
        photoTagId: photoTag?.photoTagId?.toInt(),
        photoCombinetypeId: widget.level,
        isEdited: false,
      );

      final success = await _photoService.savePhotoToTask(
        photo: photo,
        taskId: taskId,
        folderId: photoTag?.photoTagId?.toInt(),
      );

      if (success) {
        setState(() {
          final newImageId =
              '${section.sectionId}_cannot_upload_${section.images.length}';
          final newPhotoItem = PhotoItem(
            id: newImageId,
            url: 'cannot_upload', // Placeholder URL for cannot upload photos
            description: 'Cannot upload',
            photoId: photoId,
            isFromDatabase: true,
            isCannotUpload: true,
          );
          section.images.add(newPhotoItem);
          section.textControllers
              .add(TextEditingController(text: newPhotoItem.description));
        });

        // if (mounted) {
        //   SnackBarService.success(
        //     context: context,
        //     message: 'Cannot upload photo saved successfully!',
        //   );
        // }
      } else {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to save cannot upload photo to database.',
          );
        }
      }
    } catch (e) {
      logger('Error saving cannot upload photo to database: $e');
      rethrow;
    }
  }

  Widget _buildBody() {
    if (_sections.isEmpty) {
      return const EmptyState(message: "No photo sections defined.");
    }
    return ListView.separated(
      itemCount: _sections.length,
      padding: const EdgeInsets.all(16),
      separatorBuilder: (context, index) => const Gap(24),
      itemBuilder: (context, index) {
        final section = _sections[index];
        return _buildSectionView(section, index);
      },
    );
  }

  Widget _buildSectionView(PhotoSection section, int sectionIndex) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _buildCameraRowItem(section, sectionIndex),
        if (section.images.isNotEmpty) ...[
          // const Gap(16),
          ..._buildSectionImages(section, sectionIndex),
        ]
      ],
    );
  }

  Widget _buildCameraRowItem(PhotoSection section, int sectionIndex) {
    // Determine camera icon color based on photoTag settings
    final photoTag = section.photoTag;
    final Color cameraIconColor;
    final Color cameraBackgroundColor;

    if (photoTag?.isMandatory == true) {
      cameraIconColor = AppColors.darkYellow15; // Yellow/orange for mandatory
      cameraBackgroundColor = AppColors.darkYellow15.withValues(alpha: 0.1);
    } else {
      cameraIconColor = AppColors.loginGreen; // Green for optional
      cameraBackgroundColor = AppColors.loginGreen.withValues(alpha: 0.1);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _onCameraTap(sectionIndex),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: cameraBackgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.camera_alt, color: cameraIconColor, size: 24),
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(section.sectionTitle,
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall),
                    const Gap(4),
                    Text(
                      'Tap to add photos',
                      style: Theme.of(context).textTheme.montserratTableSmall,
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right,
                  color: AppColors.blackTint1, size: 24),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildSectionImages(PhotoSection section, int sectionIndex) {
    switch (_currentMode) {
      case ViewMode.stack:
        return _buildStackImages(section, sectionIndex);
      case ViewMode.list:
        return _buildListImages(section, sectionIndex);
      case ViewMode.edit:
        return _buildEditImages(section, sectionIndex);
    }
  }

  List<Widget> _buildStackImages(PhotoSection section, int sectionIndex) {
    return section.images.map((image) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildStackImageCard(image),
      );
    }).toList();
  }

  List<Widget> _buildListImages(PhotoSection section, int sectionIndex) {
    return section.images.map((image) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: _buildListImageCard(image),
      );
    }).toList();
  }

  List<Widget> _buildEditImages(PhotoSection section, int sectionIndex) {
    return section.images.asMap().entries.map((entry) {
      final imageIndex = entry.key;
      final image = entry.value;
      final controller = section.textControllers[imageIndex];
      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildEditImageCard(image, controller, sectionIndex, imageIndex),
      );
    }).toList();
  }

  Widget _buildStackImageCard(PhotoItem image) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
              color: AppColors.black10,
              blurRadius: 4,
              offset: const Offset(0, 2))
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: AspectRatio(
              aspectRatio: 1.0,
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: _buildImageWidget(image),
              ),
            ),
          ),
          if (image.description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(image.description,
                  style: Theme.of(context).textTheme.montserratParagraphSmall),
            ),
        ],
      ),
    );
  }

  Widget _buildListImageCard(PhotoItem image) {
    return Container(
      height: 132,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
              color: AppColors.black10,
              blurRadius: 4,
              offset: const Offset(0, 2))
        ],
      ),
      child: Row(
        children: [
          AspectRatio(
            aspectRatio: 1.0,
            child: Container(
              width: 120,
              height: 120,
              margin: const EdgeInsets.all(16),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                child: _buildImageWidget(image),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 16, 16, 16),
              child: Align(
                alignment: Alignment.topLeft,
                child: Text(
                    image.description.isNotEmpty
                        ? image.description
                        : 'No description',
                    style:
                        Theme.of(context).textTheme.montserratParagraphSmall),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditImageCard(PhotoItem image, TextEditingController controller,
      int sectionIndex, int imageIndex) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                    color: AppColors.black10,
                    blurRadius: 4,
                    offset: const Offset(0, 2))
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 240,
                        height: 240,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                              color:
                                  AppColors.blackTint2.withValues(alpha: 0.3),
                              width: 0.5),
                        ),
                        child: AspectRatio(
                          aspectRatio: 1.0,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _buildImageWidget(image),
                          ),
                        ),
                      ),
                      const Expanded(child: SizedBox()),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: TextField(
                    controller: controller,
                    maxLines: 1,
                    decoration: InputDecoration(
                      hintText: 'Enter description...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: AppColors.blackTint1.withValues(alpha: 0.3)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                            color: AppColors.blackTint1.withValues(alpha: 0.3)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide:
                            const BorderSide(color: AppColors.primaryBlue),
                      ),
                      contentPadding: const EdgeInsets.all(12),
                    ),
                    style: Theme.of(context).textTheme.montserratFormsField,
                    onChanged: (value) {
                      image.description = value;
                      if (image.isFromDatabase && image.photoId != null) {
                        _updatePhotoCaption(image.photoId!, value);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        const Gap(16),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Gap(8),
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColors.black60, width: 0.5),
              ),
              child: IconButton(
                icon: Icon(Icons.delete_outline,
                    size: 20, color: AppColors.black60),
                onPressed: () => _onDeleteImage(sectionIndex, imageIndex),
                padding: EdgeInsets.zero,
              ),
            ),
            const Gap(16),
            Transform.scale(
              scale: 1.5,
              child: Checkbox(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
                fillColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return AppColors.primaryBlue;
                    }
                    return Colors.white;
                  },
                ),
                side: const BorderSide(color: AppColors.blackTint2),
                checkColor: Colors.white,
                value: _isImageSelected(image.id),
                onChanged: (value) => _toggleImageSelection(image.id),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildImageWidget(PhotoItem image) {
    if (image.isCannotUpload) {
      return Container(
        color: Colors.black.withValues(alpha: 0.1),
        child: Icon(
          Icons.image_not_supported_outlined,
          color: Colors.black.withValues(alpha: 0.5),
          size: 60,
        ),
      );
    }

    return OfflineImageWidget(
      url: image.url,
      fit: BoxFit.cover,
      borderRadius: 0,
    );
  }

  Future<void> _updatePhotoCaption(int photoId, String caption) async {
    try {
      if (widget.taskId == null) return;
      final taskId = int.tryParse(widget.taskId!);
      if (taskId == null) return;

      final success = await _photoService.updatePhotoCaption(
        photoId: photoId,
        caption: caption,
        taskId: taskId,
        folderId: null,
      );

      if (success) {
        logger('Photo caption updated for ID: $photoId');
      } else {
        logger('Failed to update photo caption for ID: $photoId');
      }
    } catch (e) {
      logger('Error updating photo caption: $e');
    }
  }

  void _onDeleteImage(int sectionIndex, int imageIndex) {
    ConfirmDialog.show(
      context: context,
      title: 'Delete image',
      message: 'Are you sure you want to delete this image?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: () async {
        context.router.maybePop();

        if (sectionIndex < _sections.length &&
            imageIndex < _sections[sectionIndex].images.length) {
          final section = _sections[sectionIndex];
          final image = section.images[imageIndex];

          // Use PhotoService to handle the deletion logic
          // It will automatically handle soft delete for server images and hard delete for local images
          if (image.isFromDatabase &&
              image.photoId != null &&
              widget.taskId != null) {
            final taskId = int.tryParse(widget.taskId!);
            if (taskId != null) {
              final deleteSuccess = await _photoService.deletePhotoFromTask(
                photoId: image.photoId!,
                taskId: taskId,
                folderId: section
                    .photoTagId, // Use the section's photoTagId as folderId
              );

              if (deleteSuccess) {
                // The PhotoService handles the database logic:
                // - For local images (with localPath): removes the record completely
                // - For server images (with photoUrl): sets userDeletedPhoto = true

                // Only delete the local file if it's a local-only image (not a server image)
                // Check if the image URL doesn't start with http/https (indicating it's not a server image)
                if (ImageStorageUtils.isLocalFile(image.url)) {
                  await ImageStorageUtils.deleteImage(image.url);
                }

                setState(() {
                  _selectedImageIds.remove(image.id);
                  section.textControllers[imageIndex].dispose();
                  section.images.removeAt(imageIndex);
                  section.textControllers.removeAt(imageIndex);
                });

                if (mounted) {
                  SnackBarService.success(
                    context: context,
                    message: 'Photo deleted successfully!',
                  );
                }
              } else {
                if (mounted) {
                  SnackBarService.error(
                    context: context,
                    message: 'Failed to delete photo from database.',
                  );
                }
              }
            } else {
              if (mounted) {
                SnackBarService.error(
                  context: context,
                  message: 'Invalid task ID format.',
                );
              }
            }
          } else {
            // Image not in database - just remove from UI and delete local file if it exists
            if (ImageStorageUtils.isLocalFile(image.url)) {
              await ImageStorageUtils.deleteImage(image.url);
            }

            setState(() {
              _selectedImageIds.remove(image.id);
              section.textControllers[imageIndex].dispose();
              section.images.removeAt(imageIndex);
              section.textControllers.removeAt(imageIndex);
            });

            if (mounted) {
              SnackBarService.success(
                context: context,
                message: 'Photo deleted successfully!',
              );
            }
          }
        }
      },
    );
  }

  void _onDeleteSelectedImages() {
    final selectedCount = _selectedImageIds.length;
    ConfirmDialog.show(
      context: context,
      title: 'Delete images',
      message:
          'Are you sure you want to delete $selectedCount selected image${selectedCount > 1 ? 's' : ''}?',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: () async {
        context.router.maybePop();

        final taskId =
            widget.taskId != null ? int.tryParse(widget.taskId!) : null;

        // Create a list of images to delete (copy to avoid modification during iteration)
        final imagesToDelete = <PhotoItem>[];
        final controllersToDispose = <TextEditingController>[];
        final sectionsToUpdate = <PhotoSection>[];

        // Collect all images to delete and their controllers
        for (final section in _sections) {
          for (int i = section.images.length - 1; i >= 0; i--) {
            final image = section.images[i];
            if (_selectedImageIds.contains(image.id)) {
              imagesToDelete.add(image);
              controllersToDispose.add(section.textControllers[i]);
              if (!sectionsToUpdate.contains(section)) {
                sectionsToUpdate.add(section);
              }
            }
          }
        }

        int deletedCount = 0;

        // Delete images using PhotoService for proper soft/hard delete logic
        for (final image in imagesToDelete) {
          bool deleteSuccess = false;

          // Use PhotoService to handle deletion logic for database images
          if (image.isFromDatabase && image.photoId != null && taskId != null) {
            // Find the section this image belongs to for proper folderId
            PhotoSection? imageSection;
            for (final section in _sections) {
              if (section.images.contains(image)) {
                imageSection = section;
                break;
              }
            }

            deleteSuccess = await _photoService.deletePhotoFromTask(
              photoId: image.photoId!,
              taskId: taskId,
              folderId: imageSection
                  ?.photoTagId, // Use the section's photoTagId as folderId
            );

            if (deleteSuccess) {
              deletedCount++;
              // For local images, also delete the local file if it's in app storage
              if (ImageStorageUtils.isLocalFile(image.url) &&
                  image.url.contains('/app_')) {
                await ImageStorageUtils.deleteImage(image.url);
              }
            }
          } else {
            // Image not in database - just delete local file if it exists
            if (ImageStorageUtils.isLocalFile(image.url)) {
              await ImageStorageUtils.deleteImage(image.url);
            }
            deleteSuccess = true;
            deletedCount++;
          }
        }

        // Update UI by removing successfully deleted images and controllers
        setState(() {
          // Dispose controllers first
          for (final controller in controllersToDispose) {
            controller.dispose();
          }

          // Remove images and controllers from sections (in reverse order to maintain indices)
          for (final section in _sections) {
            for (int i = section.images.length - 1; i >= 0; i--) {
              final image = section.images[i];
              if (_selectedImageIds.contains(image.id)) {
                section.images.removeAt(i);
                section.textControllers.removeAt(i);
              }
            }
          }

          // Clear selected images
          _selectedImageIds.clear();
        });

        if (mounted) {
          if (deletedCount == selectedCount) {
            SnackBarService.success(
              context: context,
              message:
                  'Successfully deleted $deletedCount image${deletedCount > 1 ? 's' : ''}!',
            );
          } else if (deletedCount > 0) {
            SnackBarService.warning(
              context: context,
              message:
                  'Deleted $deletedCount of $selectedCount image${selectedCount > 1 ? 's' : ''}. Some deletions failed.',
            );
          } else {
            SnackBarService.error(
              context: context,
              message: 'Failed to delete any images.',
            );
          }
        }
      },
    );
  }

  void _toggleImageSelection(String imageId) {
    setState(() {
      if (_selectedImageIds.contains(imageId)) {
        _selectedImageIds.remove(imageId);
      } else {
        _selectedImageIds.add(imageId);
      }
    });
  }

  bool _isImageSelected(String imageId) {
    return _selectedImageIds.contains(imageId);
  }

  void _toggleSelectAll() {
    setState(() {
      if (_selectedImageIds.length == _totalImages) {
        _selectedImageIds.clear();
      } else {
        _selectedImageIds.clear();
        for (final section in _sections) {
          for (final image in section.images) {
            _selectedImageIds.add(image.id);
          }
        }
      }
    });
  }
}
